plugins {
    id "com.android.application"
    id "kotlin-android"
    id 'com.google.gms.google-services'
    id "dev.flutter.flutter-gradle-plugin"
}
dependencies {
    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:33.1.2')


    // TODO: Add the dependencies for Firebase products you want to use
    // When using the BoM, don't specify versions in Firebase dependencies
    // https://firebase.google.com/docs/android/setup#available-libraries
}
android {
    namespace = "com.xcodie.indyguide"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "25.1.8937393"
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.xcodie.indyguide"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24
        targetSdk = flutter.targetSdkVersion
        versionCode = 7
        versionName = "1.0.3"
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}
