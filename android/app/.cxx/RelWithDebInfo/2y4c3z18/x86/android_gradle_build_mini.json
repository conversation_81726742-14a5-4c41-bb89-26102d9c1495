{"buildFiles": ["/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/projects/indyguide/android/app/.cxx/RelWithDebInfo/2y4c3z18/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/projects/indyguide/android/app/.cxx/RelWithDebInfo/2y4c3z18/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}