class NotificationSetting {
  int? id;
  int? userId;
  int? receiveAll;
  int? tripRequestUpdates;
  int? paymentStatusUpdates;
  int? tripReminders;
  int? messageNotifications;
  int? pushNotifications;
  int? emailNotifications;

  NotificationSetting(
      {this.id,
        this.userId,
        this.receiveAll,
        this.tripRequestUpdates,
        this.paymentStatusUpdates,
        this.tripReminders,
        this.messageNotifications,
        this.pushNotifications,
        this.emailNotifications});

  NotificationSetting.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    receiveAll = json['receive_all'];
    tripRequestUpdates = json['trip_request_updates'];
    paymentStatusUpdates = json['payment_status_updates'];
    tripReminders = json['trip_reminders'];
    messageNotifications = json['message_notifications'];
    pushNotifications = json['push_notifications'];
    emailNotifications = json['email_notifications'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['receive_all'] = this.receiveAll;
    data['trip_request_updates'] = this.tripRequestUpdates;
    data['payment_status_updates'] = this.paymentStatusUpdates;
    data['trip_reminders'] = this.tripReminders;
    data['message_notifications'] = this.messageNotifications;
    data['push_notifications'] = this.pushNotifications;
    data['email_notifications'] = this.emailNotifications;
    return data;
  }
}
