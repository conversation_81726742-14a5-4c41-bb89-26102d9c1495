class Notifications {
  int? id;
  String? title;
  String? message;
  int? isRead;
  String? formattedCreatedAt;

  Notifications(
      {this.id,
        this.title,
        this.message,
        this.isRead,
        this.formattedCreatedAt});

  Notifications.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    message = json['message'];
    isRead = json['is_read'];
    formattedCreatedAt = json['formatted_created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['message'] = this.message;
    data['is_read'] = this.isRead;
    data['formatted_created_at'] = this.formattedCreatedAt;
    return data;
  }
}
