class UserModel {
  int? id;
  String? firstName;String? lastName;
  String? email;
  int? isVerified;
  int? isApproved;
  int? isActive;
  int? role;
  String? fcmToken;
  String? image;
  String? longitude;
  String? latitude;
  String? createdAt;
  String? updatedAt;
  String? imageUrl;
  String? country; String? countryCode;
  String? phone;
  String? instagramLink;

  UserModel(
      {this.id,
        this.firstName,this.lastName,
        this.email,
        this.isVerified,this.countryCode,
        this.isApproved,
        this.isActive,
        this.role,
        this.fcmToken,
        this.image,
        this.longitude,
        this.latitude,
        this.createdAt,
        this.updatedAt,
        this.imageUrl,
        this.country,
        this.phone,
        this.instagramLink});

  UserModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
  firstName = json['first_name'];lastName = json['last_name'];
    email = json['email'];
    isVerified = json['is_verified'];
    isApproved = json['is_approved'];
    isActive = json['is_active'];
    role = json['role'];
    fcmToken = json['fcm_token'];
    image = json['image'];
    longitude = json['longitude'];
    latitude = json['latitude'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    imageUrl = json['image_url'];
    country = json['country']; countryCode = json['country_code'];
    phone = json['phone'];
    instagramLink = json['instagram_link'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['email'] = this.email;
    data['is_verified'] = this.isVerified;
    data['is_approved'] = this.isApproved;data['first_name'] = this.firstName;data['last_name'] = this.lastName;
    data['is_active'] = this.isActive;
    data['role'] = this.role;
    data['fcm_token'] = this.fcmToken;
    data['image'] = this.image;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['image_url'] = this.imageUrl;
    data['country'] = this.country;data['country_code'] = this.countryCode;
    data['phone'] = this.phone;
    data['instagram_link'] = this.instagramLink;
    return data;
  }
}
