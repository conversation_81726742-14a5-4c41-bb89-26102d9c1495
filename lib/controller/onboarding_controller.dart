import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/widgets/circular_index_widget.dart';

import '../core/constants/assets_constants.dart';
import '../model/onboardingmodel.dart';

class OnboardingController extends GetxController {
  Rx<int> currentPage = 1.obs;

  final List<SliderObject> sliderData = [

    SliderObject(
      subTitle:"Explore diverse destinations, curated tours, and personalized experiences tailored for you.",

      title: "Find & Book Unique Experiences" ,
      image: Assets.onboarding1,
    ),

    SliderObject(
      title:" Seamless Booking & Payments",
      subTitle:"Book trips with ease and manage secure payments directly within the app",
      image: Assets.onboarding2,
    ),

    SliderObject(
      title:  "Host & Earn as a Local Guide",
      subTitle: "Offer your tours, manage bookings, and connect with travelers to grow your business.",
      image: Assets.onboarding3,
    ),


  ];

  Widget ProperCircleIndexWidget(int index) {
    return index == currentPage.value
        ? CircularIndexWidget(color: Colors.white,)
        : CircularIndexWidget(color: Colors.grey[800],);
  }

  onPageChanged(int index) {
    currentPage.value = index + 1;
  }
}

