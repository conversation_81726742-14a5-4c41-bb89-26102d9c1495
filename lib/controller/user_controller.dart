import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/inbox/controller/chat_controller.dart';
import 'package:indyguide/view/host/bottom_nav_bar/view/host_navigation_bar.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/controller/home_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/controller/trip_request_controller.dart';
import 'package:indyguide/view/traveller/navigation/view/traveller_navigation_view.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../core/constants/api_endpoints.dart';
import '../core/services/http_service.dart';
import '../core/widgets/widgets.dart';
import '../model/user_model.dart';
import '../view/authentication/view/login_view.dart';
import '../view/host/bottom_nav_bar/controller/nav_controller.dart';
import '../view/host/bottom_nav_bar/modules/booking/controller/booking_controller.dart';
import '../view/host/bottom_nav_bar/modules/home/<USER>/home_controller.dart';
import '../view/host/bottom_nav_bar/modules/settings/controller/profile_controller.dart';
import '../view/starting/onboarding_view.dart';
import '../view/traveller/navigation/controller/nav_controller.dart';
import '../view/traveller/navigation/modules/booking/controller/booking_controller.dart';
import '../view/traveller/navigation/modules/hosts/controller/search_controller.dart';
import '../view/traveller/navigation/modules/inbox/controller/chat_controller.dart';
import '../view/traveller/navigation/modules/settings/controller/profile_controller.dart';
import '../view/traveller/navigation/modules/trip_requests/model/city_model.dart';
import '../view/traveller/navigation/modules/trip_requests/view/create_request_view.dart';

class UserController extends GetxController {
  UserModel? userModel;
  String? token;
  String? password;
  bool isNewAccount = false;
  List<CityModel> cities = [];
  TextEditingController passwordController = TextEditingController();
  // var member = MemberDetail().obs; // Observable variable for member details
@override
  void onInit() {
    fetchUser();

    super.onInit();
    loadCities();
  }
  Future<void> saveUser(UserModel userModel, String token,bool? newAccount) async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      final userString = jsonEncode(userModel);
      sharedUser.setString('user', userString);
      sharedUser.setString('token', token);
      sharedUser.setBool('isNewAccount', newAccount ?? false);
      log('User Successfully Saved');

    } catch (e) {
      print('Error saving user: $e');
    }
  }
  Future<void> saveStatus() async {
    try {
      final SharedPreferences sharedUser =
      await SharedPreferences.getInstance();

      sharedUser.setBool('isNewAccount',false);
      log('User Successfully Saved');

    } catch (e) {
      print('Error saving user: $e');
    }
  }

  Future<void> eraseUser() async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      sharedUser.remove('user');
      sharedUser.remove('token');
      sharedUser.remove('isNewAccount');
      sharedUser.clear();
      update();
    } catch (e) {
      print('Error erasing user: $e');
    }
  }

  Future<void> fetchUser() async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      userModel = UserModel.fromJson(jsonDecode(sharedUser.getString('user')!));
      token = sharedUser.getString('token');
      isNewAccount = sharedUser.getBool('isNewAccount') ?? false;

      update();
    } catch (e) {
      print('Error fetching user: $e');
    }
  }

  void navigateToNextScreen() async {
    await fetchUser();
    Timer(const Duration(seconds: 2), () {
       Get.offAll(
            () => token != null ? userModel?.role==1? TravellerNavView():const HostNavView() :  BoardingView(),
      );
    });



  }

  fetchUserDetails() async {
    try {
      var response = await ApiService.getData(Endpoints.fetchProfile);

      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['user']);

        saveUser(userModel, token ?? "",false);
        fetchUser();

        update();
      } else {}
    } catch (e) {
    } finally {}
  }

  logout() async {
    // Delete Traveller Controllers
    Get.delete<TravellerTripRequestController>();
    Get.delete<TravellerNavController>();
    Get.delete<ProfileController>();
    Get.delete<TravellerHomeController>();

    // Delete Host Controllers
    Get.delete<HostNavController>();
    Get.delete<HostHomeController>();
    Get.delete<HostProfileController>();
    Get.delete<HostBookingController>();

    // Delete Common Controllers
    Get.delete<ChatController>();
    Get.delete<SearchHostsController>();
    Get.delete<HostBookingController>();
    Get.delete<TravellerBookingController>();
    Get.delete<HostChatController>();

    await eraseUser();
    Get.offAll(
      () => LoginView(),
    );
  }

  void requestDeletionAccount() async {
    try {
      Widgets.showLoader("Loading.. ");

      var response = await ApiService.getData(Endpoints.deleteAccount);
      Get.back();
      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("Success", "Account Deleted");
        logout();
      } else {}
    } catch (e) {
      Widgets.hideLoader();
    }
  }

  void requestLogoutAccount() async {
    try {
      Get.back();
      Widgets.showLoader("Loading.. ");

      var response = await ApiService.getData(Endpoints.logoutAccount);

      Widgets.hideLoader();

      if (response.status == true) {
        logout();
      } else {}
    } catch (e) {
      Widgets.hideLoader();
    }
  }
  Future<List<CityModel>> getCitiesFromJson() async {

    String jsonString = await rootBundle.loadString('assets/city/cities.json');

    // Parse the JSON string into a list of maps
    List<dynamic> jsonList = json.decode(jsonString);

    // Map the JSON data to a list of Country objects
    List<CityModel> countries = jsonList.map((item) {
      return CityModel.fromJson(item);
    }).toList();

    return countries;
  }
   updateFcmToken() async {
    try {
      String? token = await getToken();
      var response = await ApiService.postData(
          Endpoints.updateToken, {"fcm_token": token});
    } catch (e) {}
  }

  loadCities()
  async {
    try {
      cities = await getCitiesFromJson();

   update();
    }catch(e)
    {
      print(e.toString());
    }
  }

  getToken() async {
    final FirebaseMessaging Fcm = FirebaseMessaging.instance;

    String? token = await Fcm.getToken();
    log("Firebase Messaging Token: $token");
    return token;
  }

}
