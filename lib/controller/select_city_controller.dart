import 'package:get/get.dart';
import 'package:indyguide/controller/user_controller.dart';

import '../view/traveller/navigation/modules/trip_requests/model/city_model.dart';

class SelectCityController extends GetxController {
  RxList<CityModel> citiesForSelectedCountry = <CityModel>[].obs;
  RxList<CityModel> filteredCities = <CityModel>[].obs;
  late UserController userController;
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    userController = Get.find();
  }

  void updateCitiesForCountry(String country) {citiesForSelectedCountry.clear();filteredCities.clear();
    citiesForSelectedCountry.value = userController.cities
        .where(
            (city) => city.countryName?.toLowerCase() == country.toLowerCase())
        .toList();
    filteredCities.value = citiesForSelectedCountry;
  }

  void filterSearchResults(String query) {
    List<CityModel> searchResult = citiesForSelectedCountry.where((city) {
      return city.name!.toLowerCase().contains(query.toLowerCase());
    }).toList();

    filteredCities.value = searchResult;
  }
}
