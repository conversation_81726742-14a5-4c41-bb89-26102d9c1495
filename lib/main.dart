import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/view/host/bottom_nav_bar/view/host_navigation_bar.dart';
import 'package:indyguide/view/starting/splash_view.dart';
import 'package:indyguide/view/traveller/navigation/view/traveller_navigation_view.dart';



Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  AwesomeNotifications().createNotificationFromJsonData(message.data);

  print('Handling a background message: ${message.messageId}');
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp],
  );
  await Firebase.initializeApp();
  AwesomeNotifications().initialize(
    // Set the icon and configure channels in this method
      null, // Use default icon or specify a custom one
      [
        NotificationChannel(
          channelKey: 'default_channel_id',
          channelName: 'Basic notifications',
          channelDescription: 'Notification channel for basic tests',
          defaultColor: ColorConstants.primaryColor,
          ledColor: Colors.white,
          importance: NotificationImportance.High,
        ),
      ],
      debug: false);

  AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
    if (!isAllowed) {
      AwesomeNotifications().requestPermissionToSendNotifications();
    }
  });
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  runApp(MyApp());
}


class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    //Set the fit size (Find your UI design, look at the dimensions of the device screen and fill it in,unit in dp)
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_ , child) {
        return GetMaterialApp(
          getPages: AppRoutes.routes,
          builder: EasyLoading.init(),

          debugShowCheckedModeBanner: false,
          title: 'Indiy',
          theme: ThemeData(fontFamily: "InstrumentSansRegular",
            primarySwatch: Colors.blue,primaryColor: ColorConstants.primaryColor,scaffoldBackgroundColor: Colors.white,
            textTheme: Typography.englishLike2018.apply(fontSizeFactor: 1.sp),
          ),
          home: child,
        );
      },
      child: SplashView(),
    );
  }
}