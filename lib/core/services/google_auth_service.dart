import 'package:google_sign_in/google_sign_in.dart';
import '../widgets/widgets.dart';

class GoogleAuthService {
  static final GoogleSignIn _googleSignIn = GoogleSignIn();

  static Future<String?> signInWithGoogle() async {
    try {
      Widgets.showLoader("Signing in with Google...");

      // Check if user is already signed in
      final currentUser = await _googleSignIn.signInSilently();
      if (currentUser != null) {
        await _googleSignIn.signOut();
      }

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        Widgets.hideLoader();
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Return only the ID token
      return googleAuth.accessToken;

    } catch (error) {
      print('Google Sign-In Error: $error');
      Widgets.showSnackBar('Error', 'Failed to sign in with Google');
      return null;
    } finally {
      Widgets.hideLoader();
    }
  }

  static Future<void> signOut() async {
    await _googleSignIn.signOut();
  }
}