import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:dart_pusher_channels/dart_pusher_channels.dart';
import 'package:flutter/cupertino.dart';
import 'dart:io';

class SocketService {
  static PusherChannelsClient? _client;
  static Channel? _channel;
  static Map<String, Channel> _chatChannels = {};
  static Map<String, Channel> _userChannels = {};
  static Map<String, Channel> _adminChannels = {};

  //chat channel
  static Map<String, StreamSubscription<ChannelReadEvent>> _deleteSubscriptions = {};
  static Map<String, StreamSubscription<ChannelReadEvent>> _editSubscriptions = {};
  static Map<String, StreamSubscription<ChannelReadEvent>> _messageReadSubscriptions = {};


//user channel
  static Map<String, StreamSubscription<ChannelReadEvent>> _unReadMessageCountSubscriptions = {};
  static Map<String, StreamSubscription<ChannelReadEvent>> _unReadNotificationCountSubscriptions = {};
  static Map<String, StreamSubscription<ChannelReadEvent>> _userOnlineStatusSubscriptions = {};



  //admin channel
  static Map<String, StreamSubscription<ChannelReadEvent>> _messageRecieveSubscriptions = {};
  static Map<String, StreamSubscription<ChannelReadEvent>> _deleteAdminMessageSubscriptions = {};
  static Map<String, StreamSubscription<ChannelReadEvent>> _editAdminMessageSubscriptions = {};


  static bool isInitialized = false;
  static bool isReconnecting = false;
  static Timer? _reconnectionTimer;
  static const int _reconnectInterval = 5; // seconds
  static int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 10;
  static StreamSubscription<ChannelReadEvent>? _messageSubscription;
  static Function(Map<String, dynamic>)? _messageReadCallback;
  static Function(Map<String, dynamic>)? _messageCallback;
  static Function(Map<String, dynamic>)? _editCallback;
  static Function(Map<String, dynamic>)? _deleteCallback;
  static Function(Map<String, dynamic>)? _unReadMessagesCallback;
  static Function(Map<String, dynamic>)? _unReadNotificationsCallback;
  static Function(Map<String, dynamic>)? _onlineStatusCallback;
  static Function(Map<String, dynamic>)? _unAdminMessageRecievedCallback;
  static Function(Map<String, dynamic>)? _editAdminCallback;
  static Function(Map<String, dynamic>)? _deleteAdminCallback;
  static Future<void> initializeSocket() async {
    if (isInitialized && _client != null) {
      print('Socket already initialized');
      return;
    }

    try {
      print('Initializing socket connection');
      const hostOptions = PusherChannelsOptions.fromHost(
        scheme: 'ws',
        host: 'app.indyguide.com',
        key: 'ontmtlalzlmmftmptddk',
        shouldSupplyMetadataQueries: true,
        metadata: PusherChannelsOptionsMetadata.byDefault(),
        port: 8080,
      );

      _client = PusherChannelsClient.websocket(
        options: hostOptions,
        connectionErrorHandler: (exception, trace, refresh) {
          log("Socket connection error: $exception");
          _handleConnectionError();
          // Attempt to refresh the connection
          Future.delayed(Duration(seconds: 2), () {
            refresh();
          });
        },
      );

      // Listen for connection established
      _client?.onConnectionEstablished.listen((event) {
        log('Socket connection established successfully');
        _handleConnectionSuccess();
        _channel?.subscribe();
      });

      // // Listen for connection failed
      // _client?.o.listen((event) {
      //   print('Socket connection failed');
      //   _handleConnectionError();
      // });
      //
      // // Listen for connection terminated
      // _client?.onConnectionTerminated.listen((event) {
      //   print('Socket connection terminated');
      //   _handleConnectionError();
      // });

      _channel = _client?.publicChannel('chat');

      await _client?.connect();

      // Cancel existing subscriptions if any
      await _messageSubscription?.cancel();
      await _unsubscribeFromAllChatChannels();
      await _unsubscribeFromAllUserChannels();
await _unsubscribeFromAllAdminChannels();
      // Set up message sent subscription
      _messageSubscription = _channel?.bind('App\\Events\\MessageSent').listen((event) {
        _handleSocketEvent(event, _messageCallback, 'MessageSent');
      });

    } catch (e) {
      log('Socket initialization error: $e');
      isInitialized = false;
      _handleConnectionError();
      rethrow;
    }
  }

  static void _handleConnectionSuccess() {
    isInitialized = true;
    isReconnecting = false;
    _resetReconnectionState();

    // Resubscribe to all active channels
    _resubscribeToChannels();
  }

  static void _resubscribeToChannels() {
    // Resubscribe to chat channels
    _chatChannels.forEach((chatId, channel) {
      subscribeToChatChannel(chatId);
    });

    // Resubscribe to user channels
    _userChannels.forEach((userId, channel) {
      subscribeToUserChannel(userId);
    });

    _adminChannels.forEach((userId, channel) {
      subscribeToAdminChannel(userId);
    });
  }

  static void _handleConnectionError() {
    isInitialized = false;

    if (!isReconnecting && _reconnectAttempts < _maxReconnectAttempts) {
      isReconnecting = true;
      _startReconnectionTimer();
    }
  }

  static void _startReconnectionTimer() {
    _reconnectionTimer?.cancel();
    _reconnectionTimer = Timer.periodic(
      Duration(seconds: _reconnectInterval * (_reconnectAttempts + 1)), // Exponential backoff
      (timer) async {
        if (_reconnectAttempts >= _maxReconnectAttempts) {
          _resetReconnectionState();
          return;
        }

        _reconnectAttempts++;
        print('Attempting to reconnect (Attempt $_reconnectAttempts/$_maxReconnectAttempts)');

        try {
          final hasInternet = await _checkInternetConnection();
          if (hasInternet) {
            await initializeSocket();
          } else {
            print('No internet connection available. Will retry later.');
          }
        } catch (e) {
          print('Reconnection attempt failed: $e');
        }
      },
    );
  }

  static void _resetReconnectionState() {
    _reconnectionTimer?.cancel();
    _reconnectionTimer = null;
    _reconnectAttempts = 0;
    isReconnecting = false;
  }

  static Future<void> subscribeToChatChannel(String chatId) async {
    if (!isInitialized || _client == null) {
      initializeSocket();
    }

    try {
      // Unsubscribe from existing channel if exists
      await unsubscribeFromChatChannel(chatId);

      final channelName = 'chat.$chatId';
      print('Subscribing to channel: $channelName');

      // Create and subscribe to new channel
      final chatChannel = _client?.publicChannel(channelName);
      chatChannel?.subscribe();

      // Set up delete event listener
      final deleteSubscription = chatChannel?.bind('App\\Events\\MessageDeleted').listen((event) {
        _handleSocketEvent(event, _deleteCallback, 'MessageDeleted');
      });
      final editSubscription = chatChannel?.bind('App\\Events\\MessageEdited').listen((event) {
        _handleSocketEvent(event, _editCallback, 'MessageEdited');
      });
      // final messageReadSubscription = chatChannel?.bind('App\\Events\\MessageRead').listen((event) {
      //   _handleSocketEvent(event, _messageReadCallback, 'MessageRead');
      // });
      // Store references
      _chatChannels[chatId] = chatChannel!;
      // _messageReadSubscriptions[chatId] = messageReadSubscription!;
      _deleteSubscriptions[chatId] = deleteSubscription!;
_editSubscriptions[chatId] = editSubscription!;
      print('Successfully subscribed to chat channel: $channelName');
    } catch (e) {
      print('Error subscribing to chat channel: $e');
    }
  }
  static Future<void> subscribeToUserChannel(String userId) async {
    if (!isInitialized || _client == null) {
      log('Socket not initialized');initializeSocket();
      return;
    }

    try {
      // Unsubscribe from existing channel if exists
      await unsubscribeFromUserChannel(userId);

      final channelName = 'user.$userId';
      log('Subscribing to channel: $channelName');

      // Create and subscribe to new channel
      final userChannel = _client?.publicChannel(channelName);
      userChannel?.subscribe();
      final onlineStatusSubscription = userChannel?.bind('App\\Events\\UserOnlineStatusUpdated').listen((event) {
        _handleSocketEvent(event, _onlineStatusCallback, 'onlineStatus');
      });

      final countMessagesSubscription = userChannel?.bind('App\\Events\\UnreadCountUpdated').listen((event) {
        _handleSocketEvent(event, _unReadMessagesCallback, 'MessagesCount');
      });
      final countNotifications = userChannel?.bind('App\\Events\\UnreadNotificationsCountUpdated').listen((event) {
        _handleSocketEvent(event, _unReadNotificationsCallback, 'NotificationsCount');
      });

      // Store references
      _userChannels[userId] = userChannel!;
      _unReadMessageCountSubscriptions[userId] = countMessagesSubscription!;
      _unReadNotificationCountSubscriptions[userId] = countNotifications!;
      _userOnlineStatusSubscriptions[userId] = onlineStatusSubscription!;
      log('Successfully subscribed to chat channel: $channelName');
    } catch (e) {
      log('Error subscribing to chat channel: $e');
    }
  }
  static Future<void> subscribeToAdminChannel(String userId) async {
    if (!isInitialized || _client == null) {
      log('Socket not initialized');initializeSocket();
      return;
    }

    try {
      // Unsubscribe from existing channel if exists
      await unsubscribeFromAdminChannel(userId);

      final channelName = 'support-chat.$userId';
      log('Subscribing to channel: $channelName');

      // Create and subscribe to new channel
      final userChannel = _client?.publicChannel(channelName);
      userChannel?.subscribe();

      final recievedMessages = userChannel?.bind('App\\Events\\SupportMessageSent').listen((event) {
        _handleSocketEvent(event, _unAdminMessageRecievedCallback, 'AdminMessageRecived');
      });
      final recievedEditMessages = userChannel?.bind('App\\Events\\SupportMessageEdited').listen((event) {
        _handleSocketEvent(event, _editAdminCallback, 'AdminMessageEdited');
      });
      final recievedDeleteMessages = userChannel?.bind('App\\Events\\SupportMessageDeleted').listen((event) {
        _handleSocketEvent(event, _deleteAdminCallback, 'AdminMessageDeleted');
      });
      // Store references
      _adminChannels[userId] = userChannel!;

      _messageRecieveSubscriptions[userId] = recievedMessages!;
      _editAdminMessageSubscriptions[userId] = recievedEditMessages!;
      _deleteAdminMessageSubscriptions[userId] = recievedDeleteMessages!;
      log('Successfully subscribed to admin channel: $channelName');
    } catch (e) {
      log('Error subscribing to chat channel: $e');
    }
  }

  static Future<void> unsubscribeFromChatChannel(String chatId) async {
    final channelName = 'chat.$chatId';
    log('Unsubscribing from channel: $channelName');
    await _messageReadSubscriptions[chatId]?.cancel();
    await _deleteSubscriptions[chatId]?.cancel();
    _chatChannels[chatId]?.unsubscribe();
await _editSubscriptions[chatId]?.cancel();
    _deleteSubscriptions.remove(chatId);_editSubscriptions.remove(chatId);_messageReadSubscriptions.remove(chatId);
    _chatChannels.remove(chatId);
  }
  static Future<void> unsubscribeFromUserChannel(String userId) async {
    final channelName = 'user.$userId';
    log('Unsubscribing from channel: $channelName');

    await _unReadMessageCountSubscriptions[userId]?.cancel();
    _userChannels[userId]?.unsubscribe();
    await _unReadNotificationCountSubscriptions[userId]?.cancel();
    _unReadMessageCountSubscriptions.remove(userId);
    _unReadNotificationCountSubscriptions.remove(userId);_userOnlineStatusSubscriptions.remove(userId);
    _userChannels.remove(userId);
  }

  static Future<void> unsubscribeFromAdminChannel(String userId) async {
    final channelName = 'support-chat.$userId';
    log('Unsubscribing from channel: $channelName');

    _adminChannels[userId]?.unsubscribe();
    await _messageRecieveSubscriptions[userId]?.cancel();
    await _editAdminMessageSubscriptions[userId]?.cancel();
    await _deleteAdminMessageSubscriptions[userId]?.cancel();
    _messageRecieveSubscriptions.remove(userId);
    _editAdminMessageSubscriptions.remove(userId);
    _deleteAdminMessageSubscriptions.remove(userId);

    _adminChannels.remove(userId);
  }
  static Future<void> _unsubscribeFromAllChatChannels() async {
    for (var chatId in _chatChannels.keys.toList()) {
      await unsubscribeFromChatChannel(chatId);
    }
  }
  static Future<void> _unsubscribeFromAllUserChannels() async {
    for (var userId in _userChannels.keys.toList()) {
      await unsubscribeFromUserChannel(userId);
    }
  }
  static Future<void> _unsubscribeFromAllAdminChannels() async {
    for (var userId in _userChannels.keys.toList()) {
      await unsubscribeFromAdminChannel(userId);
    }
  }
  static void _handleSocketEvent(
    ChannelReadEvent event,
    Function(Map<String, dynamic>)? callback,
    String eventType
  ) {
    log('Socket $eventType event received: ${event.data}');
    if (callback != null && event.data != null) {
      try {
        final data = jsonDecode(event.data!);
        log('Forwarding $eventType to callback: $data');
        callback(data);
      } catch (e) {
        log('Socket $eventType parsing error: $e');
      }
    } else {
      log('No $eventType callback registered or no data received');
    }
  }

  static void listenToMessages(Function(Map<String, dynamic>) onMessageReceived) {
    log('Setting up message listener in SocketService');
    _messageCallback = onMessageReceived;
  }
  static void listenToAdminMessages(Function(Map<String, dynamic>) onMessageReceived) {
    log('Setting up message listener in SocketService');
    _unAdminMessageRecievedCallback = onMessageReceived;
  }
  static void listenToEdits(Function(Map<String, dynamic>) onMessageEdited) {
    print('Setting up edit listener in SocketService');
    _editCallback = onMessageEdited;
  }
  static void listenToAdminEdits(Function(Map<String, dynamic>) onMessageEdited) {
    print('Setting up admin edit listener in SocketService');
    _editAdminCallback = onMessageEdited;
  }
  static void listenToDeletes(Function(Map<String, dynamic>) onMessageDeleted) {
    print('Setting up delete listener in SocketService');
    _deleteCallback = onMessageDeleted;
  } static void listenToAdminDeletes(Function(Map<String, dynamic>) onMessageDeleted) {
    print('Setting up admin delete listener in SocketService');
    _deleteAdminCallback = onMessageDeleted;
  }
  static void listenToMessageRead(Function(Map<String, dynamic>) onMessageRead) {
    print('Setting up message read listener in SocketService');
    _messageReadCallback = onMessageRead;
  }
  static void listenToUnReadMessagesCount(Function(Map<String, dynamic>) onMessageCount) {
    print('Setting up unread messages count listener in SocketService');
    _unReadMessagesCallback = onMessageCount;
  }
  static void listenToUnReadNotificationCount(Function(Map<String, dynamic>) onNotificationCount) {
    print('Setting up unread notifications count listener in SocketService');
    _unReadNotificationsCallback = onNotificationCount;
  }
  static void listenToOnlineStatus(Function(Map<String, dynamic>) onUserOnlineStatus) {
    print('Setting up online user status listener in SocketService');
    _onlineStatusCallback = onUserOnlineStatus;
  }
  static void disconnect() {
    log('Disconnecting socket');
    _resetReconnectionState();
    _messageSubscription?.cancel();
    _unsubscribeFromAllChatChannels();
    _unsubscribeFromAllUserChannels();_unsubscribeFromAllAdminChannels();
    _client?.disconnect();
    _client = null;
    _channel = null;
    _chatChannels.clear();
    _deleteSubscriptions.clear();_messageRecieveSubscriptions.clear();_unAdminMessageRecievedCallback = null;
    _editSubscriptions.clear();
    isInitialized = false;
    _messageCallback = null;
    _userOnlineStatusSubscriptions.clear();
    _onlineStatusCallback = null;
    _editCallback = null;
    _unReadNotificationCountSubscriptions.clear();
    _unReadMessageCountSubscriptions.clear();
    _deleteCallback = null;
    _unReadMessagesCallback = null;
    _unReadNotificationsCallback = null;
    _messageReadCallback = null;
    _messageReadSubscriptions.clear();_deleteAdminMessageSubscriptions.clear();_editAdminMessageSubscriptions.clear();

    _deleteAdminCallback = null;_editAdminCallback = null;
  }

  // Add a method to check internet connectivity
  static Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}