import 'package:get/get.dart';
import 'package:indyguide/view/authentication/view/reset_password_view.dart';
import 'package:indyguide/view/authentication/view/signup_view.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/request_detail_view.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/settings/view/edit_profile.dart';
import 'package:indyguide/view/host/bottom_nav_bar/view/host_navigation_bar.dart';

import '../../view/authentication/view/email_verification_view.dart';
import '../../view/authentication/view/forgot_password_view.dart';
import '../../view/authentication/view/login_view.dart';
import '../../view/host/bottom_nav_bar/modules/booking/view/booking_details.dart';
import '../../view/host/bottom_nav_bar/modules/settings/view/change_password.dart';
import '../../view/starting/onboarding_view.dart';
import '../../view/starting/splash_view.dart';

class AppRoutes {
  static const splash = '/';
  static const boarding = '/boarding';
  static const userForgotPassword = '/user-forgot-password';
  static const userLogin = '/user-view';
  static const signUp = "/signup";
  static const emailVerification = '/emailVerification';
  static const navigationBar = "/navigation_bar_view";
  static const editProfile = "/editProfile";
  static const resetPassword = "/resetPassword";
  static const notificationScreen = "/notificationScreen";
  static const verificationView = "/verificationView";
  static const bookingDetails = "/bookingDetails";
  static const chatScreen = "/ChatScreen";
  static const detailView = "/detailView";
  static const changePassword = "/changePassword";
  static const createAcountVerificatiom = "/createAcountVerificatiom";
  static const notificationSetting = "/notificationSetting";
  static final routes = [
    GetPage(name: splash, page: () => SplashView()),
    GetPage(name: boarding, page: () => BoardingView()),
    GetPage(name: userForgotPassword, page: () => ForgotPasswordView()),
    GetPage(name: userLogin, page: () => LoginView()),
    GetPage(name: signUp, page: () => SignUpView()),
    GetPage(name: emailVerification, page: () => EmailVerificationView()),
    GetPage(name: navigationBar, page: () => HostNavView()),

    GetPage(name: detailView, page: () => RequestDetailView()),
    GetPage(name: changePassword, page: () => ChangePassword()),

  ];
}
