class Endpoints {
  static const domain="https://app.indyguide.com";
  static const baseURL = '$domain/api/';
  static const domainSocket="https://app.indyguide.com:8080/";

  static const socketAppKey="ontmtlalzlmmftmptddk";

  static const deleteAccount="delete-account";
  static const logoutAccount="logout";
  static const googleSignIn="google-login";

  static const privacyPolicy ='https://indyguide.com/info/privacy-policy';
  static const termsCondition ='https://indyguide.com/info/terms';
  static const fetchNotification="fetch-notifications";
  static const fetchNotificationCount="notifications/unseen-count";
  static const fetchUserTripRequests="trip-request/user-trip-requests";
  static const hostsFilter="host-profiles/filter";
  static const fetchBookings="bookings";
  static const changeBookingStatus="bookings/change-status";
  static const recommendedHosts="host-profiles/recommended";
  static const fetchHostBookings="host/bookings";
  static const changeBookingStatusHost="host/bookings/change-status";
  static const getHostTripRequests="host/trip-requests";
  static const notifications="notifications";
  static const deleteVideo="delete-video-interview";
  static const reviews="host-profiles/ratings";
  static const travellerProfile="host/traveller";

  static const userChangeTripRequest="trip-request/update-status";
  static const hostProfile="host-profiles";
  static const saveCertificate="save-certificate";
  static const deleteCertificate="delete-certificate";
  static const changeUserStatus="change-user-chat-status";
  static const changeAdminChatStatus="change-user-support-chat-status";




  static const login="login";
  static const signup="register";
  static const sendMailOtp="send-mail-otp";
  static const resendOtp="resend-otp";
  static const verifyOTP="verify-otp";
  static const requestForgotPassword="forgot-password";
  static const resendOTPForgot="forgot-password";
  static const verifyResetOTP="verify-reset-otp";
  static const resetPassword="reset-password";
  static const fetchProfile="profile";
  static const updateProfile="update-profile";
  static const updateProfileImage="update-image";
  static const createDeposit="bookings/create";
  static const sendChat="chat/send";
  static const saveRatings="bookings/save-rating";
    static const updateBannerImage="update-banner-image";
  static const updateVideo="video-interview-url";

  static const updatePassword="change-password";

  static const createTripRequest="trip-request/create";

  static const updateToken="update-fcm-token";
  static const googleKey="AIzaSyBVX21RBOY3EBzj2xEW6up5oO-H9Zp4l_Q";

  static const fetchPlacesFromGoogle =
      'https://maps.googleapis.com/maps/api/place/autocomplete/json';
  static const fetchPlaceFromGoogle =
      'https://maps.googleapis.com/maps/api/place/details/json';

  static const chats="chat";
  static const messages="$chats/messages";
  static const notificationSettings = 'notification-settings';
  static const updateNotificationSettings = 'update-notification-settings';

  static const adminSendMessages = "support-chat/send";
  static const adminMessages = "support-chat/messages";

  static const notificationCount = "unread-notifications-count";






}
