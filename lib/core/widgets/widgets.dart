import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';
import 'package:indyguide/controller/user_controller.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/trip_request_model.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/inbox/model/traveller_chat_profile_model.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/model/bookings_model.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/model/traveller_host.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/view/host_profile_detail_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/inbox/model/chat_model.dart';
import 'package:indyguide/view/traveller/navigation/modules/inbox/view/chat_view.dart';
import 'package:readmore/readmore.dart';

import '../../view/host/bottom_nav_bar/modules/booking/view/booking_details.dart';
import '../../view/host/bottom_nav_bar/modules/booking/model/booking_model.dart';
import '../../view/traveller/navigation/modules/trip_requests/model/user_trip_request_model.dart';
import '../constants/assets_constants.dart';
import '../constants/color_constants.dart';
import '../constants/padding_constants.dart';
import '../routes/app_routes.dart';
import '../utils/utils.dart';
import 'bookingscreen_card.dart';
import 'custom_button.dart';
import 'image_preview.dart';

class Widgets {
  static var heightSpaceH05 = SizedBox(
    height: 0.005.sh,
  );
  static var heightSpaceH1 = SizedBox(
    height: 0.01.sh,
  );
  static var heightSpaceH2 = SizedBox(
    height: 0.02.sh,
  );
  static var heightSpaceH3 = SizedBox(
    height: 0.03.sh,
  );
  static var heightSpaceH4 = SizedBox(
    height: 0.04.sh,
  );
  static var heightSpaceH5 = SizedBox(
    height: 0.05.sh,
  );
  static var widthSpaceW1 = SizedBox(
    width: 0.01.sw,
  );
  static var widthSpaceW2 = SizedBox(
    width: 0.02.sw,
  );
  static var widthSpaceW3 = SizedBox(
    width: 0.03.sw,
  );
  static var widthSpaceW4 = SizedBox(
    width: 0.04.sw,
  );

  static noRecordsFound({required String? title}) {
    return Center(
        child: Padding(
      padding: const EdgeInsets.only(top: 240),
      child: Text(
        title ?? "",
        style: const TextStyle(color: Colors.black54, fontSize: 16),
      ),
    ));
  }

  // static Center moreLoading() {
  //   return Center(
  //       child: Padding(
  //         padding: const EdgeInsets.all(50.0),
  //         child: CircularProgressIndicator(color: ColorConstants.primaryBlackColor),
  //       ));
  // }
  static Widget networkImage(String url,
      {double? height, double? width, int? cacheSize}) {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.cover,
      height: height,
      width: width,
      memCacheHeight: cacheSize,
      memCacheWidth: cacheSize,
      progressIndicatorBuilder: (context, url, downloadProgress) => Center(
          child: CircularProgressIndicator(
        value: downloadProgress.progress,
        color: ColorConstants.primaryColor,
      )),
      errorWidget: (context, url, error) => Image.asset(
        Assets.avatarIcon,
        height: height,
        width: width,
      ),
    );
  }

  static headerSection(
      {required String? title, required bool? backOption, Widget? widget}) {
    return AppBar(
      leading: backOption ?? false
          ? InkWell(
              onTap: () {
                Get.back();
              },
              child: Icon(
                Icons.arrow_back_ios,
                color: ColorConstants.primaryColor,
                size: 18,
              ),
            )
          : const SizedBox(),
      backgroundColor: ColorConstants.backgroundColor,
      centerTitle: true,
      title: Texts.textBold(title ?? ""),
      actions: [
        widget != null ? widget! : const SizedBox(),
        const SizedBox(
          width: 20,
        )
      ],
    );
  }

  static Center moreLoading() {
    return Center(
        child: Padding(
      padding: const EdgeInsets.all(50.0),
      child: CircularProgressIndicator(color: ColorConstants.primaryColor),
    ));
  }

  static final cardBoxDecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(15),
    boxShadow: [
      BoxShadow(
        color: Colors.black12.withOpacity(.03),
        spreadRadius: 3,
        blurRadius: 3,
        // changes position of shadow
      ),
    ],
  );
  static final searchBox = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(10),
    boxShadow: [
      BoxShadow(
        color: Colors.black12.withOpacity(.03),
        spreadRadius: 3,
        blurRadius: 3,
        // changes position of shadow
      ),
    ],
  );
  static noFound(String title) {
    return Center(
        child: Padding(
      padding: const EdgeInsets.only(top: 0),
      child: Text(
        title,
        style: const TextStyle(color: Colors.black54, fontSize: 16),
      ),
    ));
  }

  static var blockDecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(10),
    boxShadow: [
      BoxShadow(
        color: ColorConstants.greyColor, // Light grey shadow
        spreadRadius: 1, // How much the shadow spreads
        blurRadius: 10, // How soft the shadow looks
        offset: Offset(0, 3), // Positioning (horizontal, vertical)
      ),
    ],
  );
  static customDivider(
      {bool isVertical = false,
      Color? color,
      double? padding,
      double? thickness}) {
    return Padding(
      padding: padding != null
          ? EdgeInsets.symmetric(vertical: padding)
          : const EdgeInsets.only(top: 5.0),
      child: isVertical
          ? VerticalDivider(
              thickness: 1.0,
              color: color ?? Colors.black12,
            )
          : Divider(
              height: 1.0,
              thickness: thickness ?? 1.0,
              color: color ?? Colors.black12,
            ),
    );
  }
  static Widget adminChatCard() {
    return  StreamBuilder<DatabaseEvent>(
        stream: FirebaseDatabase.instanceFor(
            app: Firebase.app(),
            databaseURL:
            'https://indyguide-5602f-default-rtdb.europe-west1.firebasedatabase.app')
            .ref(
            'unread_counts/${Get.find<UserController>().userModel?.id.toString()}/support_chat/count')
            .onValue,
        builder: (context, snapshot) {
          print(snapshot.data?.snapshot.value);
        if (snapshot.hasData &&
            snapshot.data!.snapshot.exists &&
            snapshot.data!.snapshot.value !=
                null &&
            (snapshot.data!.snapshot.value
            as int) >
                0) {
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    AdvancedAvatar(
                        animated: true,
                        size: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: ColorConstants.splash,
                        ),
                        child:  Image.asset(
                          Assets.adminChatIcon,
                          color: Colors.white,
                        )
                    ),

                  ],
                ),
                Widgets.widthSpaceW3,
                Expanded(
                  child: Texts.textBlock("Admin Support",
                      size: 13, fontWeight: FontWeight.w600),
                ), CircleAvatar(
                  backgroundColor: ColorConstants.splash,
                  radius: 8,child: Text("${snapshot.data?.snapshot.value??0}",style: TextStyle(color: Colors.white,fontSize: 8),),
                ),

              ],
            ),
          );
        } else {
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    AdvancedAvatar(
                        animated: true,
                        size: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: ColorConstants.splash,
                        ),
                        child:  Image.asset(
                          Assets.adminChatIcon,
                          color: Colors.white,
                        )
                    ),

                  ],
                ),
                Widgets.widthSpaceW3,
                Expanded(
                  child: Texts.textBlock("Admin Support",
                      size: 13, fontWeight: FontWeight.w600),
                ),
              ],
            ),
          );
        }
        });
  }

  static Widget chatCard({required Chat chat}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.bottomRight,
            children: [
              AdvancedAvatar(
                animated: true,
                size: 40,
                foregroundDecoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.grey,
                    width: 0.0,
                  ),
                ),
                child: chat.otherUser?.image == null
                    ? Text(chat.otherUser!.name!.substring(0, 1) ?? "")
                    : Widgets.networkImage(chat.otherUser?.image ?? "",
                        width: 100, height: 100),
              ),
              // const Padding(
              //     padding: EdgeInsets.only(right: 1.0, bottom: 3.0),
              //     child: CircleAvatar(
              //         radius: 4,
              //         backgroundColor: Colors.white,
              //         child: CircleAvatar(
              //           radius: 3,
              //           backgroundColor: Colors.green,
              //         ))),
            ],
          ),
          Widgets.widthSpaceW3,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textBlock(chat.otherUser?.name?.split(" ")[0] ?? "",
                    size: 13, fontWeight: FontWeight.w600),
                chat.lastMessage != null
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Widgets.heightSpaceH05,
                          chat.lastMessage?.messageType != null
                              ? Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Icon(
                                        chat.lastMessage?.messageType == "image"
                                            ? Icons.camera_alt_rounded
                                            : chat.lastMessage?.messageType ==
                                                    "video"
                                                ? Icons.videocam
                                                : CupertinoIcons.doc_fill,
                                        size: 11,
                                        color: Colors.black45),
                                    SizedBox(
                                      width: 3,
                                    ),
                                    Container(
                                        alignment: Alignment.centerLeft,
                                        width: .5.sw,
                                        child: Texts.textNormal(
                                            chat.lastMessage?.message ??
                                                chat.lastMessage?.fileName ??
                                                " ",
                                            size: 12,
                                            color: chat.lastMessage?.isRead == 0
                                                ? Colors.black
                                                : Colors.black45,
                                            align: TextAlign.start,
                                            overflow: TextOverflow.ellipsis)),
                                  ],
                                )
                              : Texts.textNormal(
                                  chat.lastMessage?.message ?? "",
                                  size: 12,
                                  color: chat.lastMessage?.isRead == 0
                                      ? Colors.black
                                      : ColorConstants.textColor,
                                  align: TextAlign.start,
                                  overflow: TextOverflow.ellipsis),
                        ],
                      )
                    : SizedBox(),
              ],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                chat.lastMessage != null
                    ? chat.lastMessage?.createdAt ?? ""
                    : "",
                style: TextStyle(
                    fontSize: 8,
                    color: chat.lastMessage?.isRead == 0
                        ? ColorConstants.splash
                        : Colors.black45,
                    fontFamily: "InstrumentSansRegular"),
              ),
              SizedBox(
                height: 4,
              ),
              if (chat.lastMessage?.isRead == 0)
                CircleAvatar(
                  backgroundColor: ColorConstants.splash,
                  radius: 3,
                ),
            ],
          ),
        ],
      ),
    );
  }

  static buildRatingStar(num starValue, {bool? isCenter, double? size}) {
    Color color = starValue < 2 ? ColorConstants.splash : ColorConstants.splash;
    var starIconsMap = [1, 2, 3, 4, 5].map((e) {
      if (starValue >= e) {
        return Icon(
          Icons.star_rate,
          color: color,
          size: size ?? 14,
        );
      } else if (starValue < e && starValue > e - 1) {
        return Icon(
          Icons.star_half,
          size: size ?? 14,
          color: color,
        );
      } else {
        return Icon(
          Icons.star_border,
          color: color,
          size: size ?? 14,
        );
      }
    }).toList();

    return Row(
        mainAxisAlignment: isCenter ?? false
            ? MainAxisAlignment.center
            : MainAxisAlignment.start,
        children: starIconsMap);
  }

  // static Widget networkImage(String url,
  //     {double? height, double? width, int? cacheSize}) {
  //   return CachedNetworkImage(
  //     imageUrl: url,
  //     fit: BoxFit.cover,
  //     height: height,
  //     width: width,
  //     memCacheHeight: cacheSize,
  //     memCacheWidth: cacheSize,
  //     progressIndicatorBuilder: (context, url, downloadProgress) => Center(
  //         child: CircularProgressIndicator(value: downloadProgress.progress)),
  //     errorWidget: (context, url, error) => Image.asset(
  //       Assets.placeholder,
  //       fit: BoxFit.cover,
  //       height: height,
  //       width: width,
  //     ),
  //   );
  // }

  static profileMenu(
      {required String text,
      required IconData icon,
      required Callback press,
      required bool? isBadge,
      String? count}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
      child: TextButton(
        style: TextButton.styleFrom(
          foregroundColor: ColorConstants.primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          backgroundColor: Colors.white,
        ),
        onPressed: press,
        child: Row(
          children: [
            Stack(
              children: [
                Icon(icon),
                Positioned(
                    right: 0,
                    top: 0,
                    child: isBadge == true
                        ? CircleAvatar(
                            backgroundColor: Colors.red,
                            child: Text(
                              count!,
                              style:
                                  TextStyle(fontSize: 7, color: Colors.white),
                            ),
                            radius: 6,
                          )
                        : const SizedBox.shrink())
              ],
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  color: ColorConstants.primaryColor,
                ),
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF757575),
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  static loadingCircle() {
    return const SizedBox(
        height: 14,
        width: 14,
        child: CircularProgressIndicator(color: Colors.black, strokeWidth: 2));
  }

  static void showSnackBar(String title, String message) {
    Get.snackbar(
      icon: Icon(
        title != "Success" ? Icons.info_outline : Icons.check_circle_outline,
        color: Colors.white,
      ),
      title,
      borderColor: Colors.white,
      borderWidth: 5,
      message,
      backgroundColor: title != "Success" ? Colors.black87 : Colors.black87,
      colorText: Colors.white,
    );
  }

  static Widget buildRequestCard(
      {required UserTripRequest request, required Callback onTap,required Callback onEditTap}) {
    return Container(
      padding: EdgeInsets.all(15),
      decoration: Widgets.blockDecoration,
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Country",
                    value: request.country?.join(", ")??"",
                    icon: Assets.locationIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "Status",
                    value: request.status==0?"Deactivated":"Active",
                    icon: Assets.statusIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Start City",
                    value: request.startCity ?? "",
                    icon: Assets.cityIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "End City",
                    value: request.endCity ?? "",
                    icon: Assets.cityIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Start Date",
                    value: request.startDate ?? "",
                    icon: Assets.calendarIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "End Date",
                    value: request.endDate ?? "",
                    icon: Assets.calendarIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Budget Range",
                    value:
                        "${request.budgetMin ?? "0"}€ - ${request.budgetMax ?? "0"}€",
                    icon: Assets.dollarIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "Services",
                    value: request.interests ?? "",
                    icon: Assets.interestIcon),
              ),
            ],
          ),    Widgets.heightSpaceH2,
          const Text(
            "No of people" ?? "",
            style: TextStyle(
                fontSize: 10,
                color: Colors.black54,
                fontFamily: "InstrumentSansRegular"),
          ),
          const SizedBox(height: 2),
          Texts.textBlock(request.numberOfAttendees.toString() ?? "",
              size: 12,
              color: ColorConstants.blackColor,
              maxline: 5,
              fontWeight: FontWeight.w500),
          Widgets.heightSpaceH1,
          const Text(
            "Additional Notes" ?? "",
            style: TextStyle(
                fontSize: 10,
                color: Colors.black54,
                fontFamily: "InstrumentSansRegular"),
          ),
          const SizedBox(height: 2),
          Texts.textBlock(request.description ?? "",
              size: 12,
              color: ColorConstants.blackColor,
              maxline: 5,
              fontWeight: FontWeight.w500),
          Widgets.heightSpaceH2,

          Row(children: [
            request.status == 1
                ? Expanded(

                flex:7 ,
                child:CustomButton(
                label: "Deactivate Request",
                textColor: ColorConstants.whiteColor,
                onTap: onTap,
                radius: 50,
                fontSize: 11,
                backgroundColor: ColorConstants.redColor))
                : Expanded( flex:7 ,child:CustomButton(
                label: "Re-activate Request",
                textColor: ColorConstants.blackColor,
                onTap: onTap,
                radius: 50,
                fontSize: 11,
                backgroundColor: ColorConstants.primaryColor)),SizedBox(width: 10,),
            Expanded( flex:3 ,child: CustomButton(
                label: "Edit",
backgroundColor: ColorConstants.blackColor,
                onTap: onEditTap,textColor: ColorConstants.whiteColor,
                radius: 50,
                fontSize: 11,
               ))

          ],),

        ],
      ),
    );
  }

  static Widget buildTravellerSection(
      {HostTripRequest? request, Callback? onTap, Callback? onChatTap}) {
    return Container(
      padding: PaddingConstants.screenPaddingHalf,
      decoration: Widgets.blockDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: Widgets.networkImage(
                  request?.user?.imageUrl ?? "",
                  height: 60,
                  width: 60,
                ),
              ),
              Widgets.widthSpaceW3,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textNormal('Traveller',
                      color: ColorConstants.textColor, size: 13),
                  SizedBox(height: 3),
                  Texts.textBold(     "${request?.user?.firstName?? ""}",
                      fontWeight: FontWeight.w600, size: 15),
                ],
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                  child: buildTextGroup(
                      title: "Countries",
                      value: request?.country?.join(", ") ?? "-",
                      icon: Assets.locationIcon)),
              Widgets.widthSpaceW3,
              Expanded(
                child: buildTextGroup(
                    title: "Budget Range",
                    value:
                        "${request?.budgetMin ?? "0"}€- ${request?.budgetMax ?? "0"}€",
                    icon: Assets.dollarIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Start Date",
                    value: request?.startDate ?? "-",
                    icon: Assets.calendarIcon),
              ),
              Widgets.widthSpaceW3,
              Expanded(
                child: buildTextGroup(
                    title: "End Date",
                    value: request?.endDate ?? "-",
                    icon: Assets.calendarIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: CustomButton(
                onTap: onTap,
                label: "View Details",
                fontSize: 12,
                textColor: Colors.black,
                borderColor: ColorConstants.blackColor,
                radius: 50,
              )),
              Widgets.widthSpaceW3,
              Expanded(
                  child: CustomButton(
                onTap: onChatTap,
                label: "Chat",
                fontSize: 12,
                backgroundColor: ColorConstants.primaryColor,
                radius: 50,
                textColor: ColorConstants.blackColor,
              )),
            ],
          ),
        ],
      ),
    );
  }
  static Widget buildTravellerRequest(
      {TripRequestsChat? request}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10,horizontal: 10),
      decoration: Widgets.blockDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          Widgets.heightSpaceH1,
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                  child: buildTextGroup(
                      title: "Countries",
                      value: request?.country?.join(",") ?? "-",
                      icon: Assets.locationIcon)),
              Widgets.widthSpaceW3,
              Expanded(
                child: buildTextGroup(
                    title: "Budget Range",
                    value:
                    "${request?.budgetMin ?? "0"}€- ${request?.budgetMax ?? "0"}€",
                    icon: Assets.dollarIcon),
              ),
            ],
          ), Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Widgets.buildTextGroup(
                  title: "Start City",
                  value: request?.startCity ?? "N/A",
                  icon: Assets.cityIcon,
                ),
              ),
              Widgets.widthSpaceW3,
              Expanded(
                child: Widgets.buildTextGroup(
                  title: "End City",
                  value:  request?.endCity ?? "N/A",
                  icon: Assets.cityIcon,
                ),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Start Date",
                    value: request?.startDate ?? "-",
                    icon: Assets.calendarIcon),
              ),
              Widgets.widthSpaceW3,
              Expanded(
                child: buildTextGroup(
                    title: "End Date",
                    value: request?.endDate ?? "-",
                    icon: Assets.calendarIcon),
              ),
            ],
          ),   Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [

              Expanded(
                child: Widgets.buildTextGroup(
                  title: "Number of people",
                  value:request?.numberOfAttendees.toString()??"",
                  icon: Assets.profileIcon,
                ),
              ), Widgets.widthSpaceW3, Expanded(
                child: Widgets.buildTextGroup(
                  title: "Services",
                  value:request?.interests??"",
                  icon: Assets.interestIcon,
                ),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          const Text(
            "Additional Notes" ?? "",
            style: TextStyle(
                fontSize: 10,
                color: Colors.black54,
                fontFamily: "InstrumentSansRegular"),
          ),
          const SizedBox(height: 5),
          ReadMoreText(
              request?.description ?? "-",
              trimLines: 3,

              trimMode: TrimMode.Line,
              trimCollapsedText: 'Show more',
              trimExpandedText: 'Show less',
              lessStyle: TextStyle(
                  fontSize: 13,
                  color: Colors.black),
              moreStyle: TextStyle(
                  fontSize: 13,
                  color:  Colors.black),
              style: TextStyle(
                fontSize: 12, color: Colors.black,)), Widgets.heightSpaceH1,
        ],
      ),
    );
  }

  static Widget assetImage(String url, double width, double height) {
    return Image.asset(url, fit: BoxFit.cover, width: width, height: height);
  }

  static AppBar customAppBar({String? title, Widget? action}) {
    return AppBar(
      backgroundColor: Colors.white,
      automaticallyImplyLeading: true,
      leading: GestureDetector(
          onTap: () {
            Get.back();
          },
          child: const Icon(
            Icons.arrow_back_ios_new_outlined,
            size: 20,
          )),
      centerTitle: true,
      title:
          Texts.textMedium(title ?? "", size: 15, fontWeight: FontWeight.w500),
      actions: [action ?? SizedBox.shrink()],
    );
  }

  static buildTextGroup({String? title, String? value, String? icon}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        icon != null
            ? Row(
                children: [
                  Image.asset(
                    icon ?? "",
                    width: 14,
                    height: 14,
                    color: Colors.black54,
                  ),
                  SizedBox(
                    width: 5,
                  ),
                ],
              )
            : SizedBox(),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title ?? "",
              style: const TextStyle(
                  fontSize: 10,
                  color: Colors.black54,
                  fontFamily: "InstrumentSansRegular"),
            ),
            const SizedBox(height: 2),
            SizedBox(
                width: .30.sw,
                child: Texts.textBlock(value ?? "",
                    size: 12,
                    maxline: 10,
                    color: ColorConstants.blackColor,
                    fontWeight: FontWeight.w500)),
          ],
        ),
      ],
    );
  }

  static buildStatusTextGroup({String? title, String? value, String? icon}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        icon != null
            ? Row(
                children: [
                  Image.asset(
                    icon ?? "",
                    width: 14,
                    height: 14,
                    color: Utils.getStatusColor(value),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                ],
              )
            : SizedBox(),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title ?? "",
              style: const TextStyle(
                  fontSize: 10,
                  color: Colors.black54,
                  fontFamily: "InstrumentSansRegular"),
            ),
            const SizedBox(height: 2),
            SizedBox(
                width: .30.sw,
                child: Texts.textBlock(value ?? "",
                    size: 12,
                    maxline: 2,
                    color: Utils.getStatusColor(value),
                    fontWeight: FontWeight.w700)),
          ],
        ),
      ],
    );
  }
  static Widget buildNotificationItem({
    required String title,
    required String description,
    required String time,
  }) {
    return ListTile(
  horizontalTitleGap: 7,
      dense: true,
      leading: CircleAvatar(
        radius: 20,
        backgroundColor: ColorConstants.selectedNavIcon,
        child: Image.asset(
          Assets.notificationSettingIcon,
          height: 20,
          width: 20,
          color: Colors.white,
        ),
      ),
      title: Texts.textBlock(title,
          fontWeight: FontWeight.w600, size: 13, maxline: 2),
      trailing: Text(
        time ?? "",
        style: const TextStyle(
          fontSize: 10,
          letterSpacing: 0,
          color: Colors.black54,
        ),
      ),
      subtitle: Texts.textNormal(description,
          textAlign: TextAlign.start,
          size: 12,
          color: ColorConstants.fullBlackColor),
    );
  }
  static Widget buildBookingCard(
      {required TravellerBooking booking,
      Callback? onDetailTap,
      Callback? onChatTap,Callback? onReviewTap,
      Callback? onCancelTap}) {
    return Container(
      padding: PaddingConstants.screenPaddingHalf,
      decoration: Widgets.blockDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Widgets.heightSpaceH1,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Title",
                    value: booking.title ?? "",
                    icon: Assets.titleIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildStatusTextGroup(
                    title: "Status",
                    value: booking.status?.toUpperCase()=="PENDING"?"IN PROGRESS":booking.status?.toUpperCase(),
                    icon: Assets.statusIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Start Date",
                    value: booking.startDate ?? "",
                    icon: Assets.calendarIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "End Date",
                    value: booking.endDate ?? "",
                    icon: Assets.calendarIcon),
              ),
            ],
          ),
          heightSpaceH05,
          Divider(
            thickness: .5,
            color: ColorConstants.greyColor,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Host Name",
                    value: "${booking.guide?.firstName}",
                    icon: Assets.profileIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "Deposit Paid",
                    value: "${booking.depositAmount ?? "0"}€ ",
                    icon: Assets.dollarIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          buildTextGroup(
              title: "Location",
              value: booking.location ?? "",
              icon: Assets.locationIcon),
          Widgets.heightSpaceH3,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: CustomButton(
                padding: 9,
                onTap: onDetailTap,
                label: "Details",
                fontSize: 12,
                textColor: ColorConstants.blackColor,
                borderColor: ColorConstants.blackColor,
                radius: 50,
              )),
              Widgets.widthSpaceW2,
             booking.status?.toLowerCase() == "completed"? booking.isReviewDone==true?
             Expanded(
                  child: CustomButton(
                padding: 9,
                onTap: onChatTap,
                label: "Chat ",
                textColor: ColorConstants.blackColor,
                radius: 50,
                fontSize: 12,
                backgroundColor: ColorConstants.primaryColor,
              )):Expanded(
                  child: CustomButton(
                    padding: 9,
                    onTap: onReviewTap,
                    label: "Leave Review",
                    textColor: ColorConstants.whiteColor,
                    radius: 50,
                    fontSize: 12,
                    backgroundColor: ColorConstants.splash,
                  )):  Expanded(
                 child: CustomButton(
                   padding: 9,
                   onTap: onChatTap,
                   label: "Chat ",
                   textColor: ColorConstants.blackColor,
                   radius: 50,
                   fontSize: 12,
                   backgroundColor: ColorConstants.primaryColor,
                 )),
              Widgets.widthSpaceW2,
              booking.status?.toLowerCase() == "pending"
                  ? Expanded(
                      child: CustomButton(
                          onTap: onCancelTap,
                          padding: 9,
                          label: "Cancel",
                          textColor: ColorConstants.whiteColor,
                          radius: 50,
                          fontSize: 12,
                          backgroundColor: ColorConstants.redColor))
                  : SizedBox(),
            ],
          ),
        ],
      ),
    );
  }

  static Widget buildHostBookingCard({
    required HostBooking booking,
    Callback? onDetailTap,
    Callback? onChatTap,
    Callback? onCancelTap,
  }) {
    return Container(
      padding: PaddingConstants.screenPaddingHalf,
      decoration: Widgets.blockDecoration,
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Widgets.heightSpaceH1,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Title",
                    value: booking.title ?? "-",
                    icon: Assets.titleIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildStatusTextGroup(
                    title: "Status",
                    value: booking.status?.toUpperCase()=="PENDING"?"IN PROGRESS":booking.status?.toUpperCase(),
                    icon: Assets.statusIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Start Date",
                    value: booking.startDate ?? "-",
                    icon: Assets.calendarIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "End Date",
                    value: booking.endDate ?? "-",
                    icon: Assets.calendarIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH2,
          buildTextGroup(
              title: "Location",
              value: booking.location ?? "-",
              icon: Assets.locationIcon),
          Divider(
            thickness: .5,
            color: ColorConstants.greyColor,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: buildTextGroup(
                    title: "Traveler Name",
                    value: "${booking.user?.firstName}",
                    icon: Assets.profileIcon),
              ),
              Widgets.widthSpaceW4,
              Expanded(
                child: buildTextGroup(
                    title: "Deposit Paid",
                    value: booking.depositAmount ?? "0",
                    icon: Assets.dollarIcon),
              ),
            ],
          ),
          Widgets.heightSpaceH3,
          Widgets.heightSpaceH3,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: CustomButton(
                padding: 9,
                onTap: onDetailTap,
                label: "Details",
                fontSize: 12,
                textColor: ColorConstants.blackColor,
                borderColor: ColorConstants.blackColor,
                radius: 50,
              )),
              Widgets.widthSpaceW2,
              Expanded(
                  child: CustomButton(
                padding: 9,
                onTap: onChatTap,
                label: "Chat ",
                textColor: ColorConstants.blackColor,
                radius: 50,
                fontSize: 12,
                backgroundColor: ColorConstants.primaryColor,
              )),
              Widgets.widthSpaceW2,
              booking.status?.toLowerCase() == "pending"
                  ? Expanded(
                      child: CustomButton(
                          onTap: onCancelTap,
                          padding: 9,
                          label: "Cancel",
                          textColor: ColorConstants.whiteColor,
                          radius: 50,
                          fontSize: 12,
                          backgroundColor: ColorConstants.redColor))
                  : SizedBox(),
            ],
          ),
          Widgets.heightSpaceH1,
        ],
      ),
    );
  }

  static hostCard({TravellerHost? host}) {
    return Stack(
      children: [
        Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: ColorConstants.greyColor, // Light grey shadow
                  spreadRadius: 1, // How much the shadow spreads
                  blurRadius: 20, // How soft the shadow looks
                ),
              ],
            ),
            child: Row(children: [
              InkWell(
                onTap: () {},
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Widgets.networkImage(
                    host?.imageUrl ?? "",
                    width: 65,
                    height: 65,
                  ),
                ),
              ),
              const SizedBox(
                width: 7.0,
              ),
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock("${host?.firstName}",
                          fontWeight: FontWeight.w700,
                          maxline: 2,
                          color: Colors.black,
                          size: 15),
                      const SizedBox(
                        height: 3.0,
                      ),
                      Texts.textBlock(
                          '${host?.city ?? ""}${host?.city==null?"":","} ${host?.country ?? ""}',
                          fontWeight: FontWeight.w300,
                          color: Colors.black54,
                          size: 10),
                      host?.city==null && host?.country==null?SizedBox():const SizedBox(
                        height: 5.0,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.star,
                            color: ColorConstants.splash,
                            size: 12,
                          ),
                          Texts.textBlock(
                              ' ${host?.ratings.toString()} (${host?.reviewsCount.toString()} reviews)',
                              fontWeight: FontWeight.w300,
                              color: Colors.black54,
                              size: 10),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ])),
        Positioned(
         bottom: 20,
          right: 10,
          child: Row(children: [

            Text(host?.distance!="null"?"${host?.distance} km":"",
                style: TextStyle(fontSize: 11, color: Colors.black))
          ]),
        )
        // Positioned(
        //   top: 10,
        //   right: 10,
        //   child: Row(children: [
        //     CircleAvatar(
        //       backgroundColor: Colors.green,
        //       radius: 2,
        //     ),
        //     Text(" Active Now",
        //         style: TextStyle(fontSize: 10, color: Colors.black87))
        //   ]),
        // )
      ],
    );
  }

  static void hideLoader() {
    EasyLoading.dismiss();
  }
  //
  // static Widget widgetLoader() {
  //   return Center(
  //     child: CircularProgressIndicator(
  //       color: ColorConstants.whiteColor,
  //     ),
  //   );
  // }

  static Widget divider({bool isVertical = false}) {
    return Padding(
      padding: const EdgeInsets.only(top: 5.0),
      child: isVertical
          ? const VerticalDivider(thickness: 1.0, color: Colors.black12)
          : const Divider(height: 1.0, thickness: 1.0, color: Colors.black12),
    );
  }

  static void showLoader(String message) {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..loadingStyle = EasyLoadingStyle.custom
      ..backgroundColor = ColorConstants.fullBlackColor
      ..indicatorColor = Colors.white
      ..textColor = Colors.white
      ..maskColor = ColorConstants.fullBlackColor
      ..dismissOnTap = false;

    EasyLoading.show(
      maskType: EasyLoadingMaskType.none,
      indicator: CircularProgressIndicator(color: ColorConstants.primaryColor),
      status: message,
    );
  }

  static Future<bool> confirmationDialogue(
      BuildContext context, String title, String content) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: true,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(title),
              content: Text(content),
              actions: [
                TextButton(
                  onPressed: () => Get.back(result: false),
                  child: Text('No',
                      style: TextStyle(color: ColorConstants.primaryColor)),
                ),
                TextButton(
                  onPressed: () => Get.back(result: true),
                  child: Text('Yes',
                      style: TextStyle(color: ColorConstants.primaryColor)),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  static textTapped(
      {required String? title, required String? subTitle, required var onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: title ?? "",
                style: const TextStyle(
                  fontFamily: "RobotoRegular",
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                  color: Colors.black, // Change the color as needed
                ),
              ),
              TextSpan(
                text: " - ${subTitle ?? ""}",
                style: const TextStyle(
                  fontFamily: "RobotoRegular",
                  fontWeight: FontWeight.w400,
                  fontSize: 13,
                  color: Colors.black87, // Change the color as needed
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget BoxListItemType2(
      {required String? title1,
      required String? title3,
      Color? highlightColor,
      required String? image,
      required Function onClick}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10.0),
        child: InkWell(
          onTap: () => onClick(),
          child: Row(
            children: <Widget>[
              // ClipRRect(
              //   borderRadius: BorderRadius.circular(10),
              //   child: Widgets.networkImage(image ?? "",
              //       height: .20.sh, width: .30.sw),
              // ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: <Widget>[
                      Texts.textBlock(title1 ?? "",
                          fontWeight: FontWeight.w800,
                          color: Colors.black87,
                          maxline: 2,
                          size: 19),
                      Widgets.heightSpaceH1,
                      Texts.textBold(title3 ?? "",
                          fontWeight: FontWeight.w800,
                          color: ColorConstants.primaryColor,
                          size: 20),
                      Widgets.heightSpaceH1,
                      Row(
                        children: [
                          InkWell(
                            onTap: () {},
                            child: Container(
                              width: 35,
                              height: 30,
                              decoration: BoxDecoration(
                                // color: ColorConstants.secondaryColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Icon(
                                CupertinoIcons.minus,
                                size: 20,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            height: 45,
                            child:
                                Center(child: Texts.textBlock("0", size: 20)),
                          ),
                          InkWell(
                            onTap: () {},
                            child: Container(
                              width: 35,
                              height: 30,
                              decoration: BoxDecoration(
                                // color: ColorConstants.secondaryColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Icon(
                                CupertinoIcons.add,
                                size: 20,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
