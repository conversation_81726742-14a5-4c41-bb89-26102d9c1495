import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/core/widgets/widgets.dart';

import '../../view/traveller/navigation/modules/booking/model/booking_detail_model.dart';
import '../../view/traveller/navigation/modules/hosts/model/host_detail_model.dart';
import 'image_preview.dart';

class ReviewSection extends StatelessWidget {
  ReviewSection({
    this.image,
    this.name,
    this.dateAndTime,
    this.comment,
    this.rating = 5,
    this.images,
  });

  final String? image;
  final String? name;
  final String? dateAndTime;
  final String? comment;
  final double rating;
  final List<RatingImage>? images;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [ AdvancedAvatar(
            animated: true,
            size: 30,
            foregroundDecoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.grey,
                width: 0.0,
              ),
            ),

            child:  image==null
                ? Text(name!.substring(0, 1)):Widgets.networkImage(
                image ?? "",
                width: 100,
                height: 100),
          ),

            Widgets.widthSpaceW2,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textBlock(
                  name!,
                  size: 13,fontWeight: FontWeight.w500,
                ),
                SizedBox(
                  height: 5,
                ),
                Text(
                  dateAndTime!,
                  style: TextStyle(
                    fontSize: 9,
                    color: Colors.black54,
                    fontFamily: "InstrumentSansRegular"
                  ),
                ),
              ],
            )
          ],
        ),
        Widgets.heightSpaceH1,
        Widgets.buildRatingStar(rating),
        comment!=null?Column(crossAxisAlignment: CrossAxisAlignment.start,


          children: [Widgets.heightSpaceH05,
          Texts.textMedium(
              comment!,
              size: 12,
              color: ColorConstants.textColor
          ),],):SizedBox(),
        if (images != null && images!.isNotEmpty) ...[
          Widgets.heightSpaceH1,
          SizedBox(
            height: 100,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: images!.length,
              separatorBuilder: (context, index) => SizedBox(width: 8),
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ImagePreview(
                          imageUrl: images![index].imageUrl,
                          isNetwork: true,
                        ),
                      ),
                    );
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Widgets.networkImage(
                      images![index].imageUrl??"",
                      height: 100,
                      width: 150,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }
}
