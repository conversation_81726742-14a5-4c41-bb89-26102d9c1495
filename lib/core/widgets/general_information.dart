import 'package:flutter/material.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/core/widgets/widgets.dart';

import '../constants/color_constants.dart';

class GeneralInformation extends StatelessWidget {
  const GeneralInformation({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textMedium(
          "General Information",
          size: 20,
        ),
        Widgets.heightSpaceH2,
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textNormal("Country",
                    size: 12, color: ColorConstants.textColor),
                SizedBox(height: 1),
                Texts.textNormal("Italy",
                    size: 14, color: ColorConstants.blackColor),
              ],
            ),
            Expanded(
                child: SizedBox(
                  width: 1,
                )),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textNormal("City",
                    size: 12, color: ColorConstants.textColor),
                SizedBox(height: 1),
                Texts.textNormal("Rome",
                    size: 14, color: ColorConstants.blackColor),
              ],
            ),
            Expanded(
                child: SizedBox(
                  width: 1,
                )),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textNormal("Gender",
                    size: 12, color: ColorConstants.textColor),
                SizedBox(height: 1),
                Texts.textNormal("Male",
                    size: 14, color: ColorConstants.blackColor),
              ],
            ),
            Expanded(
                child: SizedBox(
                  width: 1,
                )),
          ],
        ),
        Widgets.heightSpaceH3,
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textNormal("Language",
                      size: 12, color: ColorConstants.textColor),
                  SizedBox(height: 1),
                  Texts.textNormal("English,Spanish,Italian,Chinese",
                      size: 14,
                      color: ColorConstants.blackColor,
                      textAlign: TextAlign.start),
                ],
              ),
            ),
            SizedBox(
              width: 15,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 41),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Texts.textNormal("Offered Countries",
                        size: 12, color: ColorConstants.textColor),
                    SizedBox(height: 1),
                    Texts.textNormal("Spain, Italy",
                        size: 14, color: ColorConstants.blackColor),
                  ],
                ),
              ),
            ),
          ],
        ),
        Widgets.heightSpaceH1,

      ],);
  }
}