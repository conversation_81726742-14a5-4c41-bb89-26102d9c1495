import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import '../../controller/onboarding_controller.dart';
import '../../model/onboardingmodel.dart';
import '../constants/padding_constants.dart';
import 'widgets.dart';

class OnBoardingPage extends StatelessWidget {
  OnboardingController onbardingController = Get.put(OnboardingController());
  final SliderObject _sliderObject;
  OnBoardingPage(this._sliderObject, {super.key});

  @override
  Widget build(BuildContext context) {
    return
 Scaffold(


   body: Stack(
alignment: Alignment.bottomCenter,
          children: [

            Image.asset(
              _sliderObject.image!,


              fit: BoxFit.cover,
              width: 1.sw,
              height: 1.sh,
              colorBlendMode: BlendMode.darken,
              color: Colors.black.withOpacity(0.5),
            ),

            Padding(
              padding: PaddingConstants.screenPaddingHalf,
              child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(child: Container()),
                Texts.textBold(_sliderObject.title!,
                  color: Colors.white,fontWeight: FontWeight.w700 ),
                  Widgets.heightSpaceH1,
                Texts.textNormal(_sliderObject.subTitle!, size: 15,color: Colors.white),
                  Widgets.heightSpaceH5,
                  Widgets.heightSpaceH5,
                  Widgets.heightSpaceH5,
                  Widgets.heightSpaceH5,
              ],),
            ),


            


          ],
        ),
 );

  }
}
