
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/widgets/video_player_screen.dart';
import 'package:indyguide/core/widgets/video_thumbnail.dart';
import 'package:indyguide/core/widgets/widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/inbox/model/meessage_model.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';

import '../../view/traveller/navigation/modules/inbox/model/chat_model.dart';
import '../constants/color_constants.dart';
import '../utils/triangle.dart';
import 'dart:math' as math;

import 'image_preview.dart';

class MessageContent extends StatelessWidget {
  final Message message;
  final Color textColor;

  const MessageContent({
    required this.message,
    required this.textColor,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (message.message?.isNotEmpty ?? false)
          Text(
            message.message ?? "",
            style: TextStyle(
              color: textColor,
              fontSize: 15,
              fontFamily: "LatoRegular",
            ),
          ),  Padding(
            padding: EdgeInsets.only(top:  message.file!=null ?8.0:3,bottom: message.fileType==null?0:8),
            child: _buildMediaContent(message.fileType ?? ""),
          ),

        //
        // Text(
        //   message.formattedCreatedAt ?? " ",
        //   style: TextStyle(
        //     color: textColor.withOpacity(0.7),
        //     fontSize: 7,
        //   ),
        //   textAlign: TextAlign.end,
        // ),
      ],
    );
  }

  Widget _buildMediaContent(String fileType) {
    switch (fileType) {
      case 'image':
        return GestureDetector(
          onTap: () => Get.to(() => ImagePreview(imageUrl: message.fileUrl ?? "", isNetwork: true,)),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Widgets.networkImage(
              message.fileUrl ?? "",
              width: 200,
              height: 200,
             
            ),
          ),
        );
      case 'video':
        return GestureDetector(
          onTap: () => Get.to(() => VideoPlayerScreen(videoUrl: message.fileUrl ?? "",isLocal: false,)),
          child: Stack(
            alignment: Alignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: message.thumbnail!=null?Widgets.networkImage(message.thumbnail ?? "",width: 200,height: 200,):Container(
                  width: 200,
                  height: 200,   color: Colors.white,
                ),
              ),
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color:ColorConstants.splash,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                    CupertinoIcons.play_arrow_solid,
                  color: Colors.black,
                  size: 30,
                ),
              ),
            ],
          ),
        );
      case 'document':
        return GestureDetector(
          onTap: () async {
            try {
              final String? url = message.fileUrl;
              if (url == null || url.isEmpty) {
                Widgets.showSnackBar('Error', 'File URL not available');
                return;
              }

              // Show download progress
              Widgets.showLoader('Downloading...');

              // Get the downloads directory
              final Directory? downloadsDir = await getExternalStorageDirectory();
              if (downloadsDir == null) {
                Widgets.hideLoader();
                Widgets.showSnackBar('Error', 'Could not access downloads folder');
                return;
              }

              // Create file name from original name or generate one
              final String fileName = message.fileName ??
                  'document_${DateTime.now().millisecondsSinceEpoch}${url.split('.').last}';
              final String filePath = '${downloadsDir.path}/$fileName';

              // Download the file
              final response = await http.get(Uri.parse(url));
              if (response.statusCode == 200) {
                final File file = File(filePath);
                await file.writeAsBytes(response.bodyBytes);

                Widgets.hideLoader();
                Widgets.showSnackBar(
                  'Success',
                  'File downloaded to Downloads folder'
                );

                // Open the file
                await OpenFile.open(filePath);
              } else {
                Widgets.hideLoader();
                Widgets.showSnackBar('Error', 'Failed to download file');
              }
            } catch (e) {
              Widgets.hideLoader();
              Widgets.showSnackBar('Error', 'Failed to download: $e');
            }
          },
          child: Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.insert_drive_file, color: Colors.grey[600]),
                SizedBox(width: 8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        message.fileName ?? "File",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[800],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4),
                      Text(
                        "Tap to download",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return SizedBox();
    }
  }
}
class MessageContentSender extends StatelessWidget {
  final Message message;
  final Color textColor;

  const MessageContentSender({
    required this.message,
    required this.textColor,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (message.message?.isNotEmpty ?? false)
          Text(
            message.message ?? "",
            style: TextStyle(
              color: textColor,
              fontSize: 15,
              fontFamily: "LatoRegular",
            ),
          ),   Padding(
          padding: EdgeInsets.only(top:  message.file!=null ?8.0:3,bottom:  message.isLoading==false?message.fileType==null?1:8:2),
          child: _buildMediaContent(message.fileType ?? ""),
        ),

      // Row(mainAxisSize: MainAxisSize.min,
      //   children: [
      //     Text(
      //         message.formattedCreatedAt ?? " ",
      //         style: TextStyle(
      //           color: Colors.black54,
      //           fontSize: 7,
      //         ),
      //         textAlign: TextAlign.end,
      //       ),
      //   ],
      // ),
      ],
    );
  }

  Widget _buildMediaContent(  String fileType) {
    switch (fileType) {
      case 'image':
        return GestureDetector(
          onTap: () => Get.to(() => ImagePreview(imageUrl: message.fileUrl ?? "", isNetwork: true,)),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Widgets.networkImage(
              message.fileUrl ?? "",
              width: 200,
              height: 200,

            ),
          ),
        );
      case 'video':
        return GestureDetector(
          onTap: () => Get.to(() => VideoPlayerScreen(videoUrl: message.fileUrl ?? "")),
          child: Stack(
            alignment: Alignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: message.thumbnail!=null?Widgets.networkImage(message.thumbnail ?? "",width: 200,height: 200,):Container(
                  width: 200,
                  height: 200,   color: Colors.white,
                ),
              ),
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color:ColorConstants.splash,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  CupertinoIcons.play_arrow_solid,
                  color: Colors.black,
                  size: 30,
                ),
              ),
            ],
          ),
        );
      case 'document':
        return GestureDetector(
          onTap: () async {
            try {
              final String? url = message.fileUrl;
              if (url == null || url.isEmpty) {
                Widgets.showSnackBar('Error', 'File URL not available');
                return;
              }

              // Show download progress
              Widgets.showLoader('Downloading...');

              // Get the downloads directory
              final Directory? downloadsDir = await getExternalStorageDirectory();
              if (downloadsDir == null) {
                Widgets.hideLoader();
                Widgets.showSnackBar('Error', 'Could not access downloads folder');
                return;
              }

              // Create file name from original name or generate one
              final String fileName = message.fileName ??
                  'document_${DateTime.now().millisecondsSinceEpoch}${url.split('.').last}';
              final String filePath = '${downloadsDir.path}/$fileName';

              // Download the file
              final response = await http.get(Uri.parse(url));
              if (response.statusCode == 200) {
                final File file = File(filePath);
                await file.writeAsBytes(response.bodyBytes);

                Widgets.hideLoader();
                Widgets.showSnackBar(
                    'Success',
                    'File downloaded to Downloads folder'
                );

                // Open the file
                await OpenFile.open(filePath);
              } else {
                Widgets.hideLoader();
                Widgets.showSnackBar('Error', 'Failed to download file');
              }
            } catch (e) {
              Widgets.hideLoader();
              Widgets.showSnackBar('Error', 'Failed to download: $e');
            }
          },
          child: Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.insert_drive_file, color: Colors.grey[600]),
                SizedBox(width: 8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        message.fileName ?? "File",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[800],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (message.file != null) ...[
                        SizedBox(height: 4),
                        Text(
                          "Tap to download",
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return SizedBox();
    }
  }
}

class ReceivedBubbleChat extends StatelessWidget {
  final Message message;
  final String? senderImage;
  final Callback? onAvatarTap;

  const ReceivedBubbleChat({
    required this.message,
    required this.onAvatarTap,
    required this.senderImage,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 20.0, left: 5, top: 0, bottom: 5),
      padding: const EdgeInsets.only(right: 20.0, left: 5, top: 2, bottom: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          GestureDetector(
            onTap: onAvatarTap,
            child: AdvancedAvatar(
              animated: true,
              size: 24,
              foregroundDecoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey, width: 0.0),
              ),
              child: Widgets.networkImage(
                senderImage ?? "",
                width: 100,
                height: 100,
              ),
            ),
          ),
          const SizedBox(width: 5),
          Flexible(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationY(math.pi),
                  child: CustomPaint(
                    painter: Triangle(ColorConstants.blackColor),
                  ),
                ),
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: ColorConstants.blackColor,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(18),
                        bottomLeft: Radius.circular(18),
                        bottomRight: Radius.circular(18),
                      ),
                    ),
                    child: MessageContent(
                      message: message,
                      textColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SentMessage extends StatelessWidget {
  final Message message;

  const SentMessage({
    required this.message,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 18.0, left: 50, top: 5, bottom:5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          Flexible(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: ColorConstants.greyColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(18),
                        bottomLeft: Radius.circular(18),
                        bottomRight: Radius.circular(18),
                      ),
                    ),
                    child: MessageContentSender(
                      message: message,
                      textColor: Colors.black87,
                    ),
                  ),
                ),
                Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationY(math.pi),
                  child: CustomPaint(
                    painter: Triangle(ColorConstants.greyColor),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
