import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_compress/video_compress.dart';

class VideoThumbnailWidget extends StatefulWidget {
  final String videoUrl;
  final double width;
  final double height;

  const VideoThumbnailWidget({
    required this.videoUrl,
    required this.width,
    required this.height,
    Key? key,
  }) : super(key: key);

  @override
  State<VideoThumbnailWidget> createState() => _VideoThumbnailWidgetState();
}

class _VideoThumbnailWidgetState extends State<VideoThumbnailWidget> {
  File? thumbnailFile;

  @override
  void initState() {
    super.initState();
    _generateThumbnail();
  }

  Future<void> _generateThumbnail() async {
    try {
      final thumbnail = await VideoCompress.getFileThumbnail(
        widget.videoUrl,
        quality: 50,
        position: -1, // -1 means the middle of the video
      );
      
      if (mounted) {
        setState(() {
          thumbnailFile = thumbnail;
        });
      }
    } catch (e) {
      print('Error generating thumbnail: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (thumbnailFile == null) {
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );
    }

    return Image.file(
      thumbnailFile!,
      width: widget.width,
      height: widget.height,
      fit: BoxFit.cover,
    );
  }

  @override
  void dispose() {
    thumbnailFile?.delete().ignore();
    super.dispose();
  }
}