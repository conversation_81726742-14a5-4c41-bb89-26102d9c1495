import 'package:flutter/material.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/core/widgets/widgets.dart';

class CertificatesAndTrainings extends StatelessWidget {
  const CertificatesAndTrainings({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textMedium("Certificates And Trainings", size: 20),
        Widgets.heightSpaceH3,
        Row(
          children: [
            Column(
              children: [
                Image.asset(
                  Assets.certificateIcon,
                  height: 20,
                  width: 20,
                ),
                Widgets.heightSpaceH2,
                Image.asset(
                  Assets.certificateIcon,
                  height: 20,
                  width: 20,
                ),
              ],
            ),
            Widgets.widthSpaceW1,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textNormal("Certificate of Incorporation",
                    size: 12),
                Widgets.heightSpaceH3,
                Texts.textNormal("Tour Operator License",
                    size: 12)
              ],
            )
          ],
        )
      ],
    );
  }
}