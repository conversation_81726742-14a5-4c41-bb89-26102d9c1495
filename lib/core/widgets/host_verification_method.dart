import 'package:flutter/cupertino.dart';
import 'package:indyguide/core/widgets/widgets.dart';

import '../constants/assets_constants.dart';
import '../constants/color_constants.dart';
import 'text_widgets.dart';

class HostVerificationMethod extends StatelessWidget {
  const HostVerificationMethod({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textMedium("Host Verification Method ",
            size: 20, color: ColorConstants.blackColor),
        Widgets.heightSpaceH3,
        Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Image.asset(
                        Assets.smsIcons,
                        height: 20,
                        width: 20,
                      ),
                      Widgets.widthSpaceW2,
                      Texts.textNormal("Email Address", size: 12),
                      Expanded(child: Widgets.widthSpaceW2),
                      Image.asset(
                        Assets.verifiedIcon,
                        height: 20,
                        width: 20,
                      )
                    ],
                  )
                ],
              ),
            ),
            Widgets.widthSpaceW4,
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Image.asset(
                        Assets.phoneIcon,
                        height: 20,
                        width: 20,
                      ),
                      Widgets.widthSpaceW2,
                      Texts.textNormal("Phone", size: 12),
                      Expanded(child: Widgets.widthSpaceW2),
                      Image.asset(
                        Assets.verifiedIcon,
                        height: 20,
                        width: 20,
                      )
                    ],
                  )
                ],
              ),
            )
          ],
        ),
        Widgets.heightSpaceH3,
        Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Image.asset(
                        Assets.passportIcon,
                        height: 20,
                        width: 20,
                      ),
                      Widgets.widthSpaceW2,
                      Texts.textNormal("Passport/IDCard", size: 12),
                      Expanded(child: Widgets.widthSpaceW2),
                      Image.asset(
                        Assets.verifiedIcon,
                        height: 20,
                        width: 20,
                      )
                    ],
                  )
                ],
              ),
            ),
            Widgets.widthSpaceW4,
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Image.asset(
                        Assets.videoCallIcon,
                        height: 20,
                        width: 20,
                      ),
                      Widgets.widthSpaceW2,
                      Texts.textNormal("Video Interview", size: 12),
                      Expanded(child: Widgets.widthSpaceW2),
                      Image.asset(
                        Assets.not_verifiedIcon,
                        height: 20,
                        width: 20,
                      )
                    ],
                  )
                ],
              ),
            )
          ],
        ),
        Widgets.heightSpaceH2,
      ],);
  }
}