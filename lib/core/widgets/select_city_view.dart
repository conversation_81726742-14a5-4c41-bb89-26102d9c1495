import 'package:country_picker/country_picker.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/controller/select_city_controller.dart';
import 'package:indyguide/controller/user_controller.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/controller/trip_request_controller.dart';

import '../constants/assets_constants.dart';
import 'entry_field.dart';
import 'widgets.dart';

class SelectCityListView extends StatefulWidget {
  final List<String> countries;

  SelectCityListView({required this.countries});

  @override
  State<SelectCityListView> createState() => _SelectCityListViewState();
}

class _SelectCityListViewState extends State<SelectCityListView> {
  late SelectCityController homeController;
  RxString selectedCountry = "".obs;

  @override
  void initState() {
    super.initState();
    homeController = Get.put(SelectCityController());
    if (widget.countries.isNotEmpty) {
      selectedCountry.value = widget.countries[0];
      homeController.updateCitiesForCountry(selectedCountry.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "Select City"),
      body: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            // Country Dropdown
            Container(
              margin: EdgeInsets.only(bottom: 15),
              decoration: BoxDecoration(
                border: Border.all(color: ColorConstants.greyColor),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Obx(() => DropdownButtonHideUnderline(
                child: DropdownButton<String>(dropdownColor: Colors.white,
                  value: selectedCountry.value,
                  isExpanded: true,
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  hint: const Text("Select Country"),
                  items: widget.countries.map((String country) {
                    return DropdownMenuItem<String>(
                      value: country,
                      child: Texts.textNormal(country,size: 14),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      selectedCountry.value = newValue;
                      homeController.updateCitiesForCountry(newValue);
                    }
                  },
                ),
              )),
            ),
            // Search Field
            EntrySearchField(
              prefixIcon: Assets.searchIcon,
              onChange: (v) {
                homeController.filterSearchResults(v ?? "");
              },
              hint: "Search City",
            ),
            Widgets.heightSpaceH2,
            // Cities List
            Obx(
              () => Expanded(
                child: Scrollbar(
                  child: ListView.separated(
                    padding: EdgeInsets.zero,
                    physics: const BouncingScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: homeController.filteredCities.length,
                    itemBuilder: (context, i) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child: ListTile(
                          contentPadding: EdgeInsets.zero,
                          onTap: () {
                            Get.back(result: {
                              'country': selectedCountry.value,
                              'city': homeController.filteredCities[i].name
                            });
                          },
                          leading: Icon(
                            Icons.location_pin,
                            color: ColorConstants.primaryColor,
                          ),
                          minLeadingWidth: 20,
                          title: Text(homeController.filteredCities[i].name ?? ""),
                          subtitle: Text(selectedCountry.value,
                              style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black45)),
                        ),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30.0),
                        child: Divider(
                          height: 1,
                          color: Colors.grey.shade100,
                        ),
                      );
                    },
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
