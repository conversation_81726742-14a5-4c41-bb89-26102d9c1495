import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/core/utils/extensions.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/padding_constants.dart';
import '../../../core/widgets/custom_button.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';

import '../../host/bottom_nav_bar/view/host_navigation_bar.dart';
import '../controller/authentication_controller.dart';




class EmailVerificationView extends StatelessWidget {
  TextEditingController otpController = TextEditingController();
  AuthenticationController authenticationController = Get.find();

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        init: authenticationController,
        builder: (_) {
          return Scaffold(
            backgroundColor: ColorConstants.backgroundColor,
            appBar: AppBar(
              backgroundColor: ColorConstants.backgroundColor,
              centerTitle: true,
              leading: GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: const Icon(
                    Icons.arrow_back_ios_new_outlined,
                    size: 15,
                  )),
              title: Image.asset(
                Assets.appLogoColorful,
                width: .40.sw,
              ),
            ),
            body:SafeArea(
              child: Padding(
                padding: PaddingConstants.screenPaddingHalf,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Widgets.heightSpaceH2,
                    Texts.textBold("Verify Your Account",
                        color: ColorConstants.blackColor,
                        size: 24,
                        textAlign: TextAlign.start),
                    Widgets.heightSpaceH1,
                    Texts.textNormal(
                      "We’ve sent a One-Time Password (OTP) to your registered email. Please enter it below to complete the verification process.",
                      color: ColorConstants.textColor,
                      size: 11,
                      textAlign: TextAlign.start,
                    ),
                    Widgets.heightSpaceH5,
                    PinCodeTextField(
                      controller: otpController,
                      appContext: context,
                      length: 6,
                      autoDisposeControllers: true,
                      animationType: AnimationType.fade,
                      textStyle: const TextStyle(
                          color: Colors.black, fontFamily: "InstrumentSansRegular"),
                      pinTheme: PinTheme(
                          fieldWidth: .12.sw,
                          shape: PinCodeFieldShape.box,
                          borderRadius: BorderRadius.circular(10),
                          borderWidth: .5,
                          selectedBorderWidth: .8,
                          activeBorderWidth: .5,
                          activeFillColor: ColorConstants.greyColor,
                          inactiveFillColor: ColorConstants.greyColor,
                          inactiveBorderWidth: .5,
                          selectedColor: ColorConstants.fullBlackColor,
                          activeColor: Colors.black12.withOpacity(.09),
                          selectedFillColor: ColorConstants.greyColor,
                          inactiveColor: Colors.grey.shade300),
                      cursorColor: ColorConstants.fullBlackColor,
                      animationDuration: Duration(milliseconds: 300),
                      enableActiveFill: true,
                      keyboardType: TextInputType.number,
                      onCompleted: (v) {
                        print("Completed");
                      },
                      onChanged: (value) {},
                      beforeTextPaste: (text) {
                        print("Allowing to paste $text");
                        //if you return true then it will show the paste confirmation dialog. Otherwise if false, then nothing will happen.
                        //but you can show anything you want here, like your pop up saying wrong paste format or etc
                        return true;
                      },
                    ),
                    Widgets.heightSpaceH2,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Texts.textNormal("Didn’t receive the OTP?  ",
                            color: Colors.black45, size: 14),
                        GestureDetector(
                          onTap: () {
                            authenticationController.resendMailOtp();
                          },
                          child: Texts.textBlock(" Resend",
                              color: ColorConstants.selectedNavIcon,
                              size: 15,
                              fontFamily: "InstrumentSansRegular",
                              fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                    Spacer(),
                    CustomButton(
                      label: "Verify OTP",
                      textColor: ColorConstants.blackColor,
                      backgroundColor: ColorConstants.primaryColor,
                      radius: 50,
                      onTap: () {
                        otpController.text.length != 6
                            ? Widgets.showSnackBar(
                            "OTP Form", "Please enter 6 digits OTP to continue")
                            :authenticationController.verifyOTP( otpController.text);
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }
}