import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/utils/extensions.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/padding_constants.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/entry_field.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../controller/authentication_controller.dart';

class ForgotPasswordView extends StatelessWidget {
  AuthenticationController authenticationController = Get.find();
  TextEditingController emailController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: ColorConstants.backgroundColor,
        appBar: AppBar(
          backgroundColor: ColorConstants.backgroundColor,
          centerTitle: true,
          leading: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: const Icon(
                Icons.arrow_back_ios_new_outlined,
                size: 15,
              )),
          title: Image.asset(
            Assets.appLogoColorful,
            width: .40.sw,
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: PaddingConstants.screenPaddingHalf,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textBold("Forgot Password?",
                    color: ColorConstants.blackColor,
                    size: 24,
                    textAlign: TextAlign.start),
                Widgets.heightSpaceH1,
                Texts.textMedium(
                    "Please enter your email address to request a password reset",
                    color: ColorConstants.textColor,
                    size: 11),
                Widgets.heightSpaceH3,
                EntryField(
                  autoFillType: const [AutofillHints.email],
                  prefixIcon: Assets.smsIcons,
                  controller: emailController,
                  textInputType: TextInputType.emailAddress,
                  hint: "Type your email",
                  // prefixIcon: Assets.mailIcon,
                ),
                Spacer(),
                CustomButton(
                  label: "Send Code",
                  textColor: ColorConstants.blackColor,
                  backgroundColor: ColorConstants.primaryColor,
                  radius: 50,
                  onTap: () {
                    if (!GetUtils.isEmail(emailController.text)) {
                      Widgets.showSnackBar(
                          "Incomplete Form", "Please enter valid email");
                    }else{

                      authenticationController.requestForgotPassword(emailController.text);
                    }

                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
