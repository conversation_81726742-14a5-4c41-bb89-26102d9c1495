import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/core/utils/extensions.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/entry_field.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../controller/authentication_controller.dart';

class LoginView extends StatelessWidget {
  AuthenticationController authenticationController =
      Get.put(AuthenticationController());
  TextEditingController emailController=TextEditingController();
  TextEditingController passwordController=TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: ColorConstants.backgroundColor,
        appBar: AppBar(
          backgroundColor: ColorConstants.backgroundColor,
          centerTitle: true,
          automaticallyImplyLeading: false,
          title: Image.asset(
            Assets.appLogoColorful,
            width: .40.sw,
          ),
        ),
        body: Padding(
          padding: PaddingConstants.screenPaddingHalf,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              formSection(context),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Texts.textNormal("Don\'t have an account?  ",
                      color: Colors.black45, size: 14),
                  GestureDetector(
                    onTap: () {
                      Get.toNamed(AppRoutes.signUp);
                    },
                    child: Texts.textBlock(" Signup",
                        color: ColorConstants.selectedNavIcon,
                        size: 15,
                        fontFamily: "InstrumentSansRegular",
                        fontWeight: FontWeight.w600),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
            ],
          ),
        ),
      ),
    );
  }

  formSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textBold("Welcome Back",
            color: ColorConstants.blackColor, size: 24),
        Widgets.heightSpaceH1,
        Texts.textNormal(
            "Sign in to explore unique travel experiences and connect with local hosts.",
            color: ColorConstants.textColor,
            size: 11,
            textAlign: TextAlign.start),
        Widgets.heightSpaceH3,
        EntryField(   autoFillType: const [AutofillHints.email],
          controller:emailController ,
          prefixIcon: Assets.smsIcons,
          hint: "Type your email",
        ),
        Obx(
          () => EntryField(

            controller: passwordController,
            prefixIcon: Assets.lockIcon,
            hint: "Type your password",
            obscureText: authenticationController.obscured.value,
            suffixIcon: authenticationController.obscured.value == false
                ? CupertinoIcons.eye_slash
                : Icons.remove_red_eye_outlined,
            onTrailingTap: () {
              authenticationController.toggleObscured();
            },
          ),
        ),   Widgets.heightSpaceH1,
        GestureDetector(
          onTap: () {
            Get.toNamed(AppRoutes.userForgotPassword);
          },
          child: Align(
            alignment: Alignment.topRight,
            child: Texts.textNormal(
              "Forgot Password?",
              color: Colors.black54,
              size: 12,
            ),
          ),
        ),
        Widgets.heightSpaceH3,
        CustomButton(
          label: "LOGIN",
          textColor: ColorConstants.blackColor,
          backgroundColor: ColorConstants.primaryColor,
          radius: 50,
          onTap: () { context.hideKeyboard();
            if (!GetUtils.isEmail(emailController.text)) {
              Widgets.showSnackBar(
                  "Incomplete Form", "Please enter valid email");
            } else if (passwordController.text.length < 6) {
              Widgets.showSnackBar("Incomplete Form",
                  "Please enter password min length 6 characters");
            }else {
              authenticationController.loginUser(emailController.text.toString(), passwordController.text.toString());
            }
          },
        ),
        Widgets.heightSpaceH2,
        Row(
          children: [
            const Expanded(
                child: Divider(
              color: Colors.black26,
              thickness: .5,
            )),
            Padding(
              padding: PaddingConstants.contentPadding,
              child: Container(
                child: Texts.textNormal("Or", color: Colors.black45, size: 12),
              ),
            ),
            const Expanded(
                child: Divider(
              color: Colors.black26,
              thickness: .5,
            )),
          ],
        ),
        Widgets.heightSpaceH2,
        // CustomButton(
        //   icon: Image.asset(
        //     Assets.gooogleIcon,
        //     height: 16,
        //     width: 16,
        //   ),
        //   label: "Continue With Google",
        //   textColor: ColorConstants.blackColor,
        //   radius: 50,
        //   borderColor: ColorConstants.blackColor,
        //   onTap: () => authenticationController.signInWithGoogle(),
        // ),
        Widgets.heightSpaceH2,
        // CustomButton(
        //   label: "Login As Guest",
        //   textColor: ColorConstants.blackColor,
        //   radius: 50,
        //   borderColor: ColorConstants.blackColor,
        //   onTap: () {
        //     Get.toNamed(AppRoutes.emailVerification);
        //   },
        // ),
      ],
    );
  }
}
