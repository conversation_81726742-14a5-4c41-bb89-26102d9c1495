import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/utils/extensions.dart';
import 'package:indyguide/core/widgets/custom_button.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/padding_constants.dart';
import '../../../core/widgets/entry_field.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../controller/authentication_controller.dart';

class ResetPasswordView extends StatelessWidget {
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  String userId; String email;

  ResetPasswordView(this.userId,this.email);

  AuthenticationController authenticationController = Get.find();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: GetBuilder(
          init: authenticationController,
          builder: (_) {
            return Scaffold(
              resizeToAvoidBottomInset: false,
              backgroundColor: ColorConstants.backgroundColor,
              appBar: AppBar(
                backgroundColor: ColorConstants.backgroundColor,
                centerTitle: true,
                leading: GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: const Icon(
                      Icons.arrow_back_ios_new_outlined,
                      size: 15,
                    )),
                title: Image.asset(
                  Assets.appLogoColorful,
                  width: .40.sw,
                ),
              ),
              body: SafeArea(
                child: Padding(
                  padding: PaddingConstants.screenPaddingHalf,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBold("Reset Password?",
                          color: ColorConstants.blackColor,
                          size: 24,
                          textAlign: TextAlign.start),
                      Widgets.heightSpaceH1,
                      Texts.textMedium(
                          "Continue using app by reset new password ",
                          color: ColorConstants.textColor,
                          size: 11),
                      Widgets.heightSpaceH3,
                      EntryField(
                        controller: passwordController,
                        hint: "Type your new password",
                        prefixIcon: Assets.lockIcon,
                        obscureText: authenticationController.obscured.value,
                        suffixIcon:
                            authenticationController.obscured.value == false
                                ? CupertinoIcons.eye_slash
                                : Icons.remove_red_eye_outlined,
                        onTrailingTap: () {
                          authenticationController.toggleObscured();
                        },
                      ),
                      EntryField(
                        controller: confirmPasswordController,
                        hint: "Type your confirm password",
                        prefixIcon: Assets.lockIcon,
                        obscureText: authenticationController.obscured.value,
                        suffixIcon:
                            authenticationController.obscured.value == false
                                ? CupertinoIcons.eye_slash
                                : Icons.remove_red_eye_outlined,
                        onTrailingTap: () {
                          authenticationController.toggleObscured();
                        },
                      ),
                      Spacer(),
                      CustomButton(
                        backgroundColor: ColorConstants.primaryColor,
                        radius: 50,
                        textColor: ColorConstants.fullBlackColor,
                        label: "Submit",
                        onTap: () { context.hideKeyboard();
                          if (passwordController.text.length < 6) {
                            Widgets.showSnackBar("Incomplete Form",
                                "Please enter password min length 6 characters");
                          } else if (passwordController.text !=
                              confirmPasswordController.text) {
                            Widgets.showSnackBar("Incomplete Form",
                                "Passwords are not matching");
                          } else {
                            authenticationController
                                .resetForgotPassword(passwordController.text,email,userId);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
    );
  }
}
