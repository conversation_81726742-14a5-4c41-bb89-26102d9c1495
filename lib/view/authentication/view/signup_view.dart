import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/utils/extensions.dart';
import 'package:indyguide/core/widgets/custom_button.dart';
import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/entry_field.dart';
import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../controller/authentication_controller.dart';

class SignUpView extends StatelessWidget {
  AuthenticationController authenticationController =
      Get.put(AuthenticationController());
  TextEditingController firstName = TextEditingController();
  TextEditingController email = TextEditingController();
  TextEditingController password = TextEditingController();
  TextEditingController lastName = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: ColorConstants.backgroundColor,
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: ColorConstants.backgroundColor,
          centerTitle: true,
          title: Image.asset(
            Assets.appLogoColorful,
            width: .40.sw,
          ),
        ),
        body: Padding(
          padding: PaddingConstants.screenPaddingHalf,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              formSection(context),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Texts.textNormal("Already have an account?  ",
                      color: Colors.black45, size: 14),
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Texts.textBlock(" Login",
                        color: ColorConstants.selectedNavIcon,
                        size: 15,
                        fontFamily: "InstrumentSansRegular",
                        fontWeight: FontWeight.w600),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
            ],
          ),
        ),
      ),
    );
  }

  formSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textBold("Join Indy Guide Today!",
            color: ColorConstants.blackColor,
            size: 24,
            textAlign: TextAlign.start),
        Widgets.heightSpaceH1,
        Texts.textNormal(
            "Create an account to discover, book, and host unforgettable travel experiences worldwide.",
            color: ColorConstants.textColor,
            size: 11,
            textAlign: TextAlign.start),
        Widgets.heightSpaceH3,
        EntryField(   autoFillType: const [AutofillHints.email],
          controller: email,
          prefixIcon: Assets.smsIcons,
          hint: "Type your email",
        ),
        Row(
          children: [
            Expanded(
              child: EntryField(
                autoFillType: const [AutofillHints.name],
                controller:firstName ,
                textCapitalization: TextCapitalization.words,
                prefixIcon: Assets.profileIcon,
                hint: "First name",
              ),
            ),SizedBox(width: 10),Expanded(
              child: EntryField(   autoFillType: const [AutofillHints.name],
                controller:lastName ,textCapitalization: TextCapitalization.words,
                prefixIcon: Assets.profileIcon,
                hint: "Last name",
              ),
            ),
          ],
        ),
        Obx(
          () => EntryField(
            controller: password,
            prefixIcon: Assets.lockIcon,
            hint: "Type your password",
            obscureText: authenticationController.obscured.value,
            suffixIcon: authenticationController.obscured.value == false
                ? CupertinoIcons.eye_slash
                : Icons.remove_red_eye_outlined,
            onTrailingTap: () {
              authenticationController.toggleObscured();
            },
          ),
        ),
        Widgets.heightSpaceH1,
        Texts.textMedium("Choose Your Role",
            fontWeight: FontWeight.w400,
            size: 12,
            color: ColorConstants.fullBlackColor),
        Widgets.heightSpaceH2,
        Row(
          children: [
            Obx(
              () => Expanded(
                child: CustomButton(
                  label: "I\'m a Traveller",
                  textColor: authenticationController.isHostSelected.value
                      ? Colors.black45
                      : ColorConstants.splash,
                  radius: 50,
                  fontSize: 10,
                  backgroundColor: authenticationController.isHostSelected.value
                      ? Colors.transparent
                      : ColorConstants.lightOrange,
                  borderColor: authenticationController.isHostSelected.value
                      ? Colors.black45
                      : ColorConstants.splash,
                  onTap: () {
                    authenticationController.toggleButton();
                  },
                ),
              ),
            ),
            Widgets.widthSpaceW2,
            Obx(
              () => Expanded(
                child: CustomButton(
                  label: "I\'m a Host",
                  textColor: !authenticationController.isHostSelected.value
                      ? Colors.black45
                      : ColorConstants.splash,
                  radius: 50,
                  fontSize: 10,
                  backgroundColor:
                      !authenticationController.isHostSelected.value
                          ? Colors.transparent
                          : ColorConstants.lightOrange,
                  borderColor: !authenticationController.isHostSelected.value
                      ? Colors.black45
                      : ColorConstants.splash,
                  onTap: () {
                    authenticationController.toggleButton();
                  },
                ),
              ),
            ),
          ],
        ),
        Widgets.heightSpaceH3,
        CustomButton(
          label: "Create Account",
          textColor: ColorConstants.blackColor,
          backgroundColor: ColorConstants.primaryColor,
          radius: 50,
          onTap: () {context.hideKeyboard();
            if(!GetUtils.isEmail(email.text)){

              Widgets.showSnackBar("Incomplete form", "Please enter valid email.");
            }
            else if(password.text.isEmpty){

              Widgets.showSnackBar("Incomplete form", "Please enter password min length 6 characters.");
            }

           else if(firstName.text.isEmpty){

              Widgets.showSnackBar("Incomplete form", "Please enter first name field");
            }
            else if(lastName.text.isEmpty){

              Widgets.showSnackBar("Incomplete form", "Please enter last name field");
            }
           else{

              authenticationController.signupUser(data: {
                "first_name": firstName.text,  "last_name": lastName.text,
                "email": email.text,
                "role": authenticationController.isHostSelected.value==true?2:1,
                "password": password.text,
                "password_confirmation": password.text,
              });
            }

          },
        ),
        // Widgets.heightSpaceH2,
        // Row(
        //   children: [
        //     const Expanded(
        //         child: Divider(
        //       color: Colors.black26,
        //       thickness: .5,
        //     )),
        //     Padding(
        //       padding: PaddingConstants.contentPadding,
        //       child: Container(
        //         child: Texts.textNormal("Or", color: Colors.black45, size: 12),
        //       ),
        //     ),
        //     const Expanded(
        //         child: Divider(
        //       color: Colors.black26,
        //       thickness: .5,
        //     )),
        //   ],
        // ),
        // Widgets.heightSpaceH2,
        // CustomButton(
        //   icon: Image.asset(
        //     Assets.gooogleIcon,
        //     height: 16,
        //     width: 16,
        //   ),
        //   label: "Continue With Google",
        //   textColor: ColorConstants.blackColor,
        //   radius: 50,
        //   borderColor: ColorConstants.blackColor,
        //   onTap: () => authenticationController.signInWithGoogle(),
        // ),
      ],
    );
  }
}
