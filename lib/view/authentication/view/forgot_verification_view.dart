import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/utils/extensions.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/padding_constants.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/widgets/custom_button.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';


import '../controller/authentication_controller.dart';

class ForgotVerificationView extends StatelessWidget {

  String? email;

  ForgotVerificationView(this.email);

  TextEditingController otpController = TextEditingController();
  AuthenticationController authenticationController =
  AuthenticationController();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: Scaffold(
        backgroundColor: ColorConstants.backgroundColor,
        appBar: AppBar(
          backgroundColor: ColorConstants.backgroundColor,
          centerTitle: true,
          title: Image.asset(Assets.appLogoColorful,height: 150,width:150 ,),
        ),
        body: SafeArea(
          child: Padding(
            padding: PaddingConstants.screenPaddingHalf,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Widgets.heightSpaceH2,
                Texts.textBold("Verify Your Account ",
                    color: ColorConstants.blackColor,
                    size: 20,
                    textAlign: TextAlign.start),
                Widgets.heightSpaceH1,
                Texts.textNormal(
                  "We’ve sent a One-Time Password (OTP) to your registered email. Please enter it below to complete the verification process.",
                  color:ColorConstants.textColor,
                  size: 13,
                  textAlign: TextAlign.start,
                ),
                Widgets.heightSpaceH1,
                Texts.textBlock(email??"",
                    color: Colors.black,
                    size: 15,
                    fontWeight: FontWeight.w700,
                    fontFamily: "InstrumentSansRegular"),
                Widgets.heightSpaceH5,
                PinCodeTextField(
                  controller: otpController,
                  appContext: context,
                  length: 6,
                  autoDisposeControllers: true,
                  animationType: AnimationType.fade,
                  textStyle: const TextStyle(color: Colors.black),
                  pinTheme: PinTheme(
                      fieldWidth: .12.sw,
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(10),
                      borderWidth: .5,
                      selectedBorderWidth: .8,
                      activeBorderWidth: .5,
                      activeFillColor: ColorConstants.silverColor,
                      inactiveFillColor: ColorConstants.silverColor,
                      inactiveBorderWidth: .5,
                      selectedColor: ColorConstants.fullBlackColor,
                      activeColor: Colors.black12.withOpacity(.09),
                      selectedFillColor: ColorConstants.whiteColor,
                      inactiveColor: Colors.black12.withOpacity(.09)),
                  cursorColor:ColorConstants.fullBlackColor,
                  animationDuration: Duration(milliseconds: 300),
                  enableActiveFill: true,

                  keyboardType: TextInputType.number,
                  onCompleted: (v) {
                    print("Completed");
                  },
                  onChanged: (value) {},
                  beforeTextPaste: (text) {
                    print("Allowing to paste $text");
                    //if you return true then it will show the paste confirmation dialog. Otherwise if false, then nothing will happen.
                    //but you can show anything you want here, like your pop up saying wrong paste format or etc
                    return true;
                  },
                ),
                Widgets.heightSpaceH1,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Texts.textNormal("00:48 ", size: 14,color: ColorConstants.textColor),
                  ],
                ),
                Widgets.heightSpaceH1,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Texts.textNormal(
                        "Didn’t receive the OTP? ",color: ColorConstants.textColor,size: 14
                    ),
                    GestureDetector(
                      onTap: () {
                        authenticationController.resendForgotOtp(email??"");
                      },
                      child: Texts.textBlock("  Resend ",
                          color: ColorConstants.selectedNavIcon,
                          size: 15,
                          fontFamily: "InstrumentSansRegular",
                          fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
                Spacer(),


                CustomButton(
                  label:"Verify OTP",    textColor: ColorConstants.blackColor,
                  backgroundColor: ColorConstants.primaryColor,

                  radius: 50,
                  onTap: () {   context.hideKeyboard();
                    otpController.text.length != 6? Widgets.showSnackBar(
                        "OTP Form", "Please enter 6 digits OTP"):


                    authenticationController.verifyForgotOTP(
                        otpController.text ?? "",email??"");



                  },
                ),
                Widgets.heightSpaceH1,


              ],
            ),
          ),
        ),
      ),
    );
  }
}
