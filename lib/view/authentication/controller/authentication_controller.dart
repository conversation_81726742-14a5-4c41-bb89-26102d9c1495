import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/view/host/bottom_nav_bar/view/host_navigation_bar.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/view/create_request_view.dart';
import 'package:indyguide/view/traveller/navigation/view/traveller_navigation_view.dart';

import '../../../controller/user_controller.dart';
import '../../../core/constants/api_endpoints.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/services/google_auth_service.dart';
import '../../../core/services/http_service.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../../../model/user_model.dart';

import '../view/email_verification_view.dart';
import '../view/forgot_verification_view.dart';
import '../view/login_view.dart';
import '../view/reset_password_view.dart';

class AuthenticationController extends GetxController {
  late UserController userController;
  RxBool isCheck = false.obs;
  RxBool is18Check = false.obs;
  RxBool obscured = false.obs;

  RxString? userID="".obs;
  RxString? email="".obs;

  void toggleObscured() {
    obscured.value = !obscured.value;
  }

  RxBool isHostSelected = false.obs;
  toggleButton() {
    isHostSelected.value = !isHostSelected.value;
  }

  @override
  void onInit() {
    super.onInit();
     userController = Get.find();
  }

  void loginUser(String emailString, String password) async {
    try {
       Widgets.showLoader("Loading.. ");

      var response = await ApiService.postData(
          Endpoints.login, {"email": emailString, "password": password});
      Widgets.hideLoader();

      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['user']);

        userID?.value = userModel.id.toString();

        if (userModel.isVerified == 1 && userModel.isActive == 1) {
          await userController.saveUser(userModel, response.data['token'],false);
          userController.fetchUser();

          if (userModel.role == 1) {
            Get.offAll(() => TravellerNavView());
          } else {
            Get.offAll(() => HostNavView());
          }

        } else if (userModel.isVerified == 0) {
           Get.to(() => EmailVerificationView());
          Widgets.showSnackBar("Error", response.message ?? "");
        }
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {

      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  void signupUser({var data}) async {
    try {
       Widgets.showLoader("Creating account..");

      var response = await ApiService.postData(Endpoints.signup, data);

      Widgets.hideLoader();

      if (response.status == true) {

        userID?.value = response.data['user_id'].toString();
        Get.off(() => EmailVerificationView());
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  requestForgotPassword(String emailString) async {
    try {
      Widgets.showLoader("Loading.. ");

      var payload = {"email": emailString};
      email?.value=emailString;
      var response =
          await ApiService.postData(Endpoints.requestForgotPassword, payload);

      Widgets.hideLoader();

      if (response.status == true) {

        Widgets.showSnackBar("Success", response.message ?? "");
        Get.to(() => ForgotVerificationView(emailString));
      } else {
        Widgets.showSnackBar("Error", response.message??"");
      }
    } catch (e) {
      Widgets.hideLoader();
    }finally{  Widgets.hideLoader();}
  }

  resendForgotOtp(String email) async {
    try {
      Widgets.showLoader("Loading.. ");

      var payload = {"email": email};

      var response =
          await ApiService.postData(Endpoints.requestForgotPassword, payload);

      Widgets.hideLoader();

      if (response.status == true) {


        Widgets.showSnackBar("Success", response.message ?? "");

      } else {
        Widgets.showSnackBar("Error",  response.message ?? "");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "Something went Wrong");
    }
  }

  resendMailOtp() async {
    try {
      Widgets.showLoader("Loading.. ");

      var payload = {"user_id": userID?.value};

      var response = await ApiService.postData(Endpoints.resendOtp, payload);

      Widgets.hideLoader();
      update();
      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");
      } else {
        Widgets.showSnackBar(
            "Error", response.message ?? "Something went wrong");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "Something went Wrong");
    } finally {
      Widgets.hideLoader();
    }
  }

  verifyOTP( String otpCode) async {
     Widgets.showLoader("Verifying OTP");
    try {
      var payload = {"user_id": userID?.value, "otp": otpCode};
      var response = await ApiService.postData(Endpoints.verifyOTP, payload);
       Widgets.hideLoader();
      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['user']);
        await userController.saveUser(userModel, response.data['token'],true);
        userController.fetchUser();

        if (userModel.role == 1) {
          Get.offAll(() => TravellerNavView());        } else {
          Get.offAll(() => const HostNavView());
        }
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Invalid Code");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", e.toString());
    }
  }

  verifyForgotOTP(String otpCode,String email) async {
    Widgets.showLoader("Verifying OTP");

    try {
      var payload = {"email": email, "otp": otpCode};
      var response =
          await ApiService.postData(Endpoints.verifyResetOTP, payload);

      Widgets.hideLoader();
      if (response.status == true) {


         Get.off(() => ResetPasswordView( response.data['user']['id'].toString() ,email));
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Invalid Code");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", e.toString());
    }
  }

  resetForgotPassword(
    String password,String email,String userId
  ) async {
    Widgets.showLoader("Loading..");

    try {
      var payload = {

        "password": password,
        "user_id": userId,"email":email,
        "password_confirmation": password
      };
      var response =
          await ApiService.postData(Endpoints.resetPassword, payload);

      Widgets.hideLoader();
      if (response.status == true) {
        userID?.value = "";
        Widgets.showSnackBar("Success", response.message.toString());
        Get.offAll(() => LoginView());
      } else {
        Widgets.showSnackBar("Error", response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", e.toString());
    }
  }

  void signInWithGoogle() async {
    try {
      final userData = await GoogleAuthService.signInWithGoogle();

      if (userData != null) {
        var response = await ApiService.postData(Endpoints.googleSignIn, {

          'oauth_token': userData,

        });

        if (response.status == true) {
          UserModel userModel = UserModel.fromJson(response.data['user']);
          await userController.saveUser(userModel, response.data['token'],false);
          userController.fetchUser();

          if (userModel.role == 1) {
            Get.offAll(() => TravellerNavView());
          } else {
            Get.offAll(() => HostNavView());
          }
        } else if (response.message == "User not found.") {
          // Show role selection dialog for new users
          Get.dialog(
            Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Texts.textBold(
                      "Choose Your Role",
                      size: 18,
                      color: ColorConstants.blackColor,
                    ),
                    Widgets.heightSpaceH2,
                    Texts.textNormal(
                      "Please select your role to continue registration",
                      color: ColorConstants.textColor,
                      size: 14,
                      textAlign: TextAlign.center,
                    ),
                    Widgets.heightSpaceH3,
                    CustomButton(
                      label: "I'm a Traveller",
                      textColor: ColorConstants.blackColor,
                      backgroundColor: ColorConstants.primaryColor,
                      radius: 50,
                      onTap: () async {
                        Get.back(); // Close dialog
                        await _registerGoogleUser(userData, 1);
                      },
                    ),
                    Widgets.heightSpaceH2,
                    CustomButton(
                      label: "I'm a Host",
                      textColor: ColorConstants.blackColor,
                      backgroundColor: ColorConstants.primaryColor,
                      radius: 50,
                      onTap: () async {
                        Get.back(); // Close dialog
                        await _registerGoogleUser(userData, 2);
                      },
                    ),
                  ],
                ),
              ),
            ),
            barrierDismissible: false,
          );
        } else {
          Widgets.showSnackBar("Error", response.message ?? "");
        }
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    }
  }

  Future<void> _registerGoogleUser(String token, int role) async {
    try {
      Widgets.showLoader("Creating account...");

      var response = await ApiService.postData(
        Endpoints.googleSignIn,
        {
          'oauth_token': token,
          'role': role,
        }
      );

      Widgets.hideLoader();

      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['user']);
        await userController.saveUser(userModel, response.data['token'], true);
        userController.fetchUser();

        if (userModel.role == 1) {
          Get.offAll(() => TravellerNavView());
        } else {
          Get.offAll(() => HostNavView());
        }
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", e.toString());
    }
  }
}
