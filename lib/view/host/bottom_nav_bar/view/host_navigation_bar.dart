import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:indyguide/core/constants/padding_constants.dart';


import '../../../../core/constants/assets_constants.dart';
import '../../../../core/constants/color_constants.dart';
import '../controller/nav_controller.dart';
import '../modules/booking/view/bookings_view.dart';
import '../modules/home/<USER>/home_controller.dart';
import '../modules/home/<USER>/home_view.dart';

import '../modules/inbox/view/inbox.dart';
import '../modules/settings/view/profile_view.dart';
import '../modules/settings/view/settings_view.dart';
class HostNavView extends StatefulWidget {
  const HostNavView({super.key});


  @override
  _HostNavViewState createState() => _HostNavViewState();
}

class _HostNavViewState extends State<HostNavView> {
  final HostNavController navController = Get.put(HostNavController());
  final HostHomeController homeController = Get.find<HostHomeController>();




  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => navController.screens[navController.currentIndex.value]),
      bottomNavigationBar: Obx(
            () => BottomNavigationBar(
          backgroundColor: Colors.white,
          elevation: 20,
          selectedLabelStyle: TextStyle(
            fontSize: 11,
            fontFamily: "InstrumentSansRegular",
            color: ColorConstants.selectedNavIcon,
            fontWeight: FontWeight.w500,
          ),
          unselectedFontSize: 11,
          unselectedLabelStyle: const TextStyle(
            fontSize: 11,
            fontFamily: "InstrumentSansRegular",
            color: Colors.black45,
          ),
          type: BottomNavigationBarType.fixed,
          selectedItemColor: ColorConstants.selectedNavIcon,
          unselectedItemColor: Colors.black45,
          currentIndex: navController.currentIndex.value,
          onTap: navController.changeIndex,
          items: [
            _navBarItem(Assets.homeIcon,Assets.homeFilledIcon, 'Trip Requests', 0),
            _navBarItem(Assets.message,Assets.messageFilled, 'Inbox', 1),
            _navBarItem(Assets.calendarIcon,Assets.calendarFilledIcon, 'Bookings', 2),

            _navBarItem(Assets.profileIcon,Assets.profileIcon, 'Profile', 3),
            _navBarItem(Assets.settingIcon,Assets.settingFilledIcon, 'Settings', 4),

          ],
        ),
      ),
    );
  }

  BottomNavigationBarItem _navBarItem(String icon, String activeIcon, String label, int index) {
    return BottomNavigationBarItem(
      icon: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(2),
            child: Obx(
              () => ColorFiltered(
                colorFilter: ColorFilter.mode(
                  navController.currentIndex.value == index
                      ? ColorConstants.selectedNavIcon
                      : Colors.black38,
                  BlendMode.srcIn,
                ),
                child: Image.asset(
                  navController.currentIndex.value == index ? activeIcon : icon,
                  height: 25,
                  width: 25,
                ),
              ),
            ),
          ),
          if (index == 1) // Inbox tab
            Positioned(
              right: 0,
              top: 0,
              child: Obx(() {
                final count = homeController.unreadMessagesCount.value;
                return count > 0
                    ? CircleAvatar(radius: 7,
                        backgroundColor: Colors.red,
                        child: Text(
                          count > 9 ? '9+' : count.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 7,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : const SizedBox.shrink();
              }),
            ),
        ],
      ),
      label: label,
    );
  }
}
