



import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/booking/controller/booking_controller.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/booking/view/bookings_view.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/home_controller.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/home_view.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/inbox/controller/chat_controller.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/inbox/view/inbox.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/settings/view/profile_view.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/settings/view/settings_view.dart';

class HostNavController extends GetxController {
  RxInt currentIndex = 0.obs;
  late HostHomeController homeController;
  late HostChatController chatController;

  late HostBookingController bookingViewController;
  @override
  void onInit() {
    super.onInit();
    homeController = Get.put(HostHomeController());
    chatController = Get.put(HostChatController());
    bookingViewController = Get.put(HostBookingController());
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: DateTime.now().millisecondsSinceEpoch ~/
              1000, // Unique ID for each notification
          channelKey: 'default_channel_id',
          title: message.notification?.title ?? 'Default Title',
          body: message.notification?.body ?? 'Default Body',
          notificationLayout: NotificationLayout.Default,
        ),
      );
    });

  }


  final List<Widget> screens = [
    HomeScreen(),HostInboxView(),HostBookingsView(),HostProfileView(),HostSettingsView()
  ];
  void changeIndex(int index) {
    currentIndex.value = index;
  }
}
