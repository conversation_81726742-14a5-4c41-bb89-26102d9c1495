import 'dart:developer';
import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/inbox/controller/chat_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/view/payment_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/model/Host_detail_model.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/model/traveller_host.dart';
import 'package:intl/intl.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/services/http_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../../model/notifications_model.dart';
import '../../../../../../model/user_model.dart';
import '../../inbox/model/traveller_chat_profile_model.dart';
import '../model/trip_request_model.dart';

class HostHomeController extends GetxController {

  RxString selectedCountry="".obs;
  RxString selectedCity="".obs;
  RxString selectedDate="".obs;
  RxDouble latitude = 0.0.obs;
  RxDouble longitude = 0.0.obs;

  late UserController userController;
  RxBool isRequestDetailLoading = false.obs;
  var hostDetail = HostDetail().obs;
  RxBool isTravellerDetailLoading = false.obs;
  var travellerDetail = TravelChatProfile().obs;

  var selectedRequest = HostTripRequest().obs;
  RxList requests = <HostTripRequest>[].obs;
  RxBool isRequestsLoading = false.obs;
  RxBool isRequestsMoreLoading = false.obs;
  RxInt totalRequests = 0.obs;
  RxInt currentRequestsPage = 0.obs;


  RxList notifications = <Notifications>[].obs;
  RxBool isNotificationLoading = false.obs;
  RxBool isNotificationMoreLoading = false.obs;
  RxInt totalNotifications = 0.obs;
  RxInt currentNotificationPage = 0.obs;
  final RxInt unreadNotificationsCount = 0.obs;
  final RxInt unreadMessagesCount = 0.obs;


  @override
  void onInit() {
    super.onInit();
    userController = Get.find();
    userController.updateFcmToken();
    fetchRecommendedTrips();
    getCurrentLocation();
    fetchUnreadNotificationsCount();
  }

  Future<void> fetchUnreadNotificationsCount() async {
    try {
      var response = await ApiService.getData(Endpoints.notificationCount);
      if (response.status == true) {
        unreadNotificationsCount.value = response.data['unread_notifications_count'] ?? 0;
        unreadMessagesCount.value = response.data['unread_messages_count'] ?? 0;

      }
    } catch (e) {
      print('Error fetching unread notifications count: $e');
    }
  }
  Future<void> getCurrentLocation() async {

    try {
      if (!await checkLocationServices()) return;
      if (!await requestLocationPermission()) return;

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      latitude.value = position.latitude;
      longitude.value = position.longitude;
      updateProfile();

    } catch (e) {
      print("Error fetching location: $e");
    } finally {

    }
  }

  Future<bool> checkLocationServices() async {
    if (!await Geolocator.isLocationServiceEnabled()) {
      print('Location services are disabled.');
      return false;
    }
    return true;
  }
  Future<void> selectDate(BuildContext context) async {


    DateTime? picked = await showDatePicker(
      context: context,
     initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (picked != null) {
      String formattedDate = Utils.formatDate(picked);
      selectedDate.value = formattedDate;
    }
  }

  fetchHostDetail() async {
    try {
      isRequestDetailLoading.value = true;
      print(selectedRequest.value.id);
      var response = await ApiService.getData(
        "${Endpoints.hostProfile}/${selectedRequest.value.id}",
      );
      isRequestDetailLoading.value = false;

      if (response.status == true) {
        hostDetail.value = HostDetail.fromJson(response.data['host']);
      }
    } finally {
      isRequestDetailLoading.value = false;
    }
  }
  fetchTravellerDetail(String id) async {
    try {
      isTravellerDetailLoading.value = true;

      var response = await ApiService.getData(
        "${Endpoints.travellerProfile}/$id",
      );
      isTravellerDetailLoading.value = false;

      if (response.status == true) {
         travellerDetail.value =TravelChatProfile.fromJson(response.data['traveller']);
      }
    } finally {
      isTravellerDetailLoading.value  = false;
    }
  }


  Future<bool> requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        print('Location permissions are denied');
        return false;
      }
    }
    if (permission == LocationPermission.deniedForever) {
      print('Location permissions are permanently denied.');
      return false;
    }
    return true;
  }
  fetchNotifications({int page = 1}) async {
    try {

      unreadNotificationsCount.value=0;
      if (isNotificationLoading.value) return;
      if (page == 1) {
        isNotificationLoading.value = true;
      } else {
        isNotificationMoreLoading.value = true;
      }
      var response = await ApiService.getData("${Endpoints.notifications}?page=$page",
          );
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          notifications.clear();
          totalNotifications.value = 0;
          currentNotificationPage.value = 1;
        }

        notifications.addAll(
          (response.data['notifications'] as List)
              .map((e) => Notifications.fromJson(e))
              .toList(),

        );

        totalNotifications.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {print(e);
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
    } finally {
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
    }
  }

  fetchRecommendedTrips({int page = 1}) async {
    try {
      if (isRequestsLoading.value) return;
      if (page == 1) {
        isRequestsLoading.value = true;
      } else {
        isRequestsMoreLoading.value = true;
      }
      var response = await ApiService.postData(Endpoints.getHostTripRequests,
          {"page": page, "country": selectedCountry.value, "city": selectedCity.value, "date": selectedDate.value});
      isRequestsLoading.value = false;
      isRequestsMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          requests.clear();
          totalRequests.value = 0;
          currentRequestsPage.value = 1;
        }

        requests.addAll(
          (response.data['trip_requests'] as List)
              .map((e) => HostTripRequest.fromJson(e))
              .toList(),

        );

        totalRequests.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isRequestsLoading.value = false;
      isRequestsMoreLoading.value = false;
    } finally {
      isRequestsLoading.value = false;
      isRequestsMoreLoading.value = false;
    }
  }

  backgroundRecommendedTrips({int page = 1}) async {
    try {
      if (page == 1) {
      } else {
        isRequestsMoreLoading.value = true;
      }
      var response = await ApiService.postData(Endpoints.getHostTripRequests,
          {"page": page, "country": selectedCountry.value, "city": selectedCity.value, "date": selectedDate.value});

      isRequestsMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          requests.clear();
          totalRequests.value = 0;
          currentRequestsPage.value = 1;
        }

        requests.addAll(
          (response.data['trip_requests'] as List)
              .map((e) => HostTripRequest.fromJson(e))
              .toList(),
        );

        totalRequests.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isRequestsMoreLoading.value = false;
    } finally {
      isRequestsMoreLoading.value = false;
    }
  }
  void clearFilters() {
    selectedCountry.value = "";
    selectedCity.value = "";
    selectedDate.value = "";
    fetchRecommendedTrips(page: 1);
  }

   updateProfile() async {
    try {



      final data = {
        'latitude': latitude.value,
        'longitude': longitude.value,};

      final response = await ApiService.postData(Endpoints.updateProfile, data);

      if (response.status == true) {
        final userModel = UserModel.fromJson(response.data['user']);
        await userController.saveUser(userModel, userController.token ?? "",false);
        await userController.fetchUser();

      } else {
      }
    } catch (e) {
    } finally {
    }
  }

}
