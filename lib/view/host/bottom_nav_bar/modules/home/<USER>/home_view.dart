import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/core/widgets/custom_button.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/core/widgets/traveller_card.dart';
import 'package:indyguide/core/widgets/widgets.dart';
import 'package:indyguide/view/host/bottom_nav_bar/controller/nav_controller.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/trip_request_model.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/notification_view.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../traveller/navigation/modules/inbox/model/chat_model.dart';
import '../../../../../traveller/navigation/modules/inbox/view/chat_view.dart';
import '../../../../../traveller/navigation/modules/settings/view/notification_screen.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/view/chat_view.dart';
import '../controller/home_controller.dart';
import 'filter_view.dart';
import 'request_detail_view.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late HostHomeController homeController;
  final ScrollController scrollController = ScrollController();


  @override
  void initState() {
    super.initState();
    homeController = Get.find();


    homeController.isRequestsLoading.value = false;
    homeController.isRequestsMoreLoading.value = false;
   homeController.selectedCountry.value="";
   homeController.selectedCity.value="";
    homeController.selectedDate.value="";
    homeController.backgroundRecommendedTrips(page: 1);
    scrollController.addListener(scrollListener);
  }
  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        homeController.totalRequests.value >
            homeController.requests.length) {
      homeController.backgroundRecommendedTrips(
          page: homeController.currentRequestsPage.value + 1);
      homeController.currentRequestsPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        title: GetBuilder<UserController>(init: UserController(),
            builder: (controller) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      AdvancedAvatar(
                        animated: true,
                        size: 35,
                        foregroundDecoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.grey,
                            width: 0.0,
                          ),
                        ),

                        child:  controller.userModel?.imageUrl == null
                            ? Text(controller.userModel!.firstName!.substring(0, 1)):Widgets.networkImage(
                            controller.userModel?.imageUrl ?? "",
                            width: 100,
                            height: 100),
                      ),

                      const SizedBox(
                        width: 10,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Texts.textNormal('Hello, Welcome Back!',
                              color: ColorConstants.textColor, size: 9),
                          Texts.textBold(                                   "${controller.userModel?.firstName} ${controller.userModel?.lastName}",
                               size: 16),
                        ],
                      ),
                    ],
                  ),
                  Stack(clipBehavior: Clip.none,
                    children: [
                      GestureDetector(
                        onTap: () {
                          homeController.fetchNotifications(page: 1);
                          Get.to(() => const HostNotificationsView());
                        },
                        child: Image.asset(
                          Assets.notificationIcon,
                          height: 22,
                          width: 22,
                        ),
                      ),
                      Positioned(
                        right: -2,
                        top: -2,
                        child: Obx(() {
                          final count = homeController.unreadNotificationsCount.value;
                          return count > 0
                              ? CircleAvatar(
                                  radius: 7,
                                  backgroundColor: Colors.red,
                                  child: Text(
                                    count > 9 ? '9+' : count.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 7,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                )
                              : const SizedBox.shrink();
                        }),
                      ),
                    ],
                  ),
                ],
              );
            }
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {

          homeController.currentRequestsPage.value = 1;
          await homeController.fetchRecommendedTrips(page: 1);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(), // Important for RefreshIndicator to work even when empty
          child: Padding(
            padding: PaddingConstants.screenPaddingLess,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Obx(() => Texts.textMedium(
                        'Trip Requests ${homeController.requests.isNotEmpty ? "(${homeController.requests.length})" : ""}',
                        size: 15)),
                    InkWell(  onTap: () {
                      Get.to(()=>HostFilterView());
                    },
                      child: Image.asset(
                        Assets.filterIcon,
                        height: 30,
                        color: Colors.black54,
                        width: 30,
                      ),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                buildTripRequestList(),

              ],
            ),
          ),
        ),
      ),
    );
  }
  Widget buildTripRequestList() {
    return Obx(
      () {
        return ListView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(), // Since parent is scrollable
          controller: scrollController,
          children: [
            homeController.isRequestsLoading.value
                ? const ShimmerListSkeleton()
                : homeController.requests.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  HostTripRequest host =
                  homeController.requests[index];
                  return Widgets.buildTravellerSection(request: host,onChatTap: (){

                    final existingChat =
                    Get.find<HostChatController>()
                        .chats
                        .firstWhereOrNull((chat) =>
                    chat.otherUser?.id ==host.user?.id);
                    existingChat == null
                        ? Get.to(() => HostChatView(
                      chatUser: OtherUser(
                        id: host.user?.id,
                        name: "${host.user?.firstName??""}",
                        image: host.user?.imageUrl,
                      ),
                    ))?.then((value) {
                      // Get.find<HostNavController>()
                      //     .changeIndex(1);

                    })
                        : Get.to(() => HostChatView(
                      chatId:
                      existingChat.chatId.toString(),
                      chatUser: OtherUser(
                        id: host.user?.id,
                        name: "${host.user?.firstName??""}",
                        image: host.user?.imageUrl,
                      ),
                    ))?.then((value) {
                      // Get.find<HostNavController>()
                      //     .changeIndex(1);

                    });

                  },onTap: (){


                    homeController.selectedRequest.value=host;

                    Get.to(()=>RequestDetailView());
                  });

                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:homeController.requests.length ?? 0)
                : Widgets.noRecordsFound(title: "No trip requests"),
            if (homeController.isRequestsMoreLoading.value)
              Widgets.moreLoading(),
          ],
        );
      },
    );
  }
}
