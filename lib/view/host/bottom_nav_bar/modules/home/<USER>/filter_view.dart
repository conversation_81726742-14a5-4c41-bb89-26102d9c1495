import 'package:country_picker/country_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/home_controller.dart';

import 'package:indyguide/view/traveller/navigation/modules/hosts/view/select_location_view.dart';

import '../../../../../../core/constants/constants_list.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/custom_dropdown.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';

class HostFilterView extends StatefulWidget {
  const HostFilterView({super.key});

  @override
  State<HostFilterView> createState() => _HostFilterViewState();
}

class _HostFilterViewState extends State<HostFilterView> {

  final HostHomeController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: CustomButton(
                label: "Clear Filters",
                fontSize: 12,
                borderColor: ColorConstants.blackColor,
                textColor: ColorConstants.blackColor,
                radius: 50,
                onTap: () {controller.clearFilters();},
              ),
            ),
            Widgets.widthSpaceW3,
            Expanded(
              child: CustomButton(
                label: "Search",
                fontSize: 12,
                borderColor: Colors.transparent,
                backgroundColor: ColorConstants.primaryColor,
                textColor: ColorConstants.blackColor,
                radius: 50,
                onTap: () {

                  Get.back();

                   controller.fetchRecommendedTrips(page: 1);


                   },


              ),
            ),
          ],
        ),
      ),
      appBar: Widgets.customAppBar(title: "Filter By"),
      body: SingleChildScrollView(
        padding: PaddingConstants.screenPaddingLess,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
Widgets.heightSpaceH3,

            Obx(
                  () => CustomDropdown(
                  onTap: () {
                   pickCountryBottomSheet(context);
                  },
                  value: controller.selectedCountry.value == ""
                      ? null
                      :controller.selectedCountry.value,
                  hint: "--Select--",
                  label: "Select Country"),
            ),
            Obx(
                  () => CustomDropdown(
                  onTap: () {
                    Get.to(()=>const SelectLocationView())?.then((result){

                      if(result!=null)
                      {
                        controller.selectedCity.value=result;
                      }

                    });;
                  },
                  value: controller.selectedCity.value == ""
                      ? null
                      :controller.selectedCity.value,
                  hint: "--Select--",
                  label: "Select City"),
            ),
            Obx(
                  () => CustomDropdown(
                  onTap: () {
                    controller.selectDate(context);
                  },
                  value: controller.selectedDate.value == ""
                      ? null
                      :controller.selectedDate.value,
                  hint: "--Select--",
                  label: "Select Date"),
            ),
            const SizedBox(
              height: 7,
            ),
            Divider(
              color: ColorConstants.greyColor,
            ),

          ],
        ),
      ),
    );
  }

  Widget buildBudgetInput(RxDouble value) {
    return Expanded(
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstants.greyColor),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Obx(() => Text(
                "${value.value.toInt()}",
                style: const TextStyle(fontSize: 13, color: Colors.black45),
              )),
            ),
            Texts.textNormal("€", color: Colors.black54, size: 14),
          ],
        ),
      ),
    );
  }
  pickCountryBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "Search Country")),
      showPhoneCode: false, // Set to true if you need phone codes
      onSelect: (Country country) {
        controller.selectedCountry.value = country.name;

      },
    );
  }

}
