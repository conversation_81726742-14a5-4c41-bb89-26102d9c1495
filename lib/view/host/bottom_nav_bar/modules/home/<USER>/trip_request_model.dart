class HostTripRequest {
  int? id;
  int? userId;
  List<String>? country;
  String? startCity;
  String? endCity;
  String? startDate;
  String? endDate;
  String? description;
  String? budgetMin;
  String? budgetMax;
  String? interests;
  int? status; int? numberOfAttendees;
  String? createdAt;
  String? updatedAt;
  User? user;

  HostTripRequest(
      {this.id,
        this.userId,
        this.country,this.numberOfAttendees,
        this.startCity,
        this.endCity,
        this.startDate,
        this.endDate,
        this.description,
        this.budgetMin,
        this.budgetMax,
        this.interests,
        this.status,
        this.createdAt,
        this.updatedAt,
        this.user});

  HostTripRequest.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    numberOfAttendees = json['no_of_attendees']??0;
    country = json['country'].cast<String>();
    startCity = json['start_city'];
    endCity = json['end_city'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    description = json['description'];
    budgetMin = json['budget_min'];
    budgetMax = json['budget_max'];
    interests = json['interests'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['country'] = this.country;
    data['start_city'] = this.startCity;
    data['end_city'] = this.endCity;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['description'] = this.description;
    data['budget_min'] = this.budgetMin;
    data['budget_max'] = this.budgetMax;
    data['interests'] = this.interests;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}

class User {
  int? id;
  String? firstName;  String? lastName;
  String? image;
  String? imageUrl;

  User({this.id, this.firstName,this.lastName, this.image, this.imageUrl});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
   firstName = json['first_name'];lastName = json['last_name'];
    image = json['image'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;data['last_name'] = this.lastName;
    data['image'] = this.image;
    data['image_url'] = this.imageUrl;
    return data;
  }
}
