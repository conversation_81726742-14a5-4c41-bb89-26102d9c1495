import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/custom_button.dart';
import 'package:readmore/readmore.dart';

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/routes/app_routes.dart';
import '../../../../../../core/widgets/bookingscreen_card.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../traveller/navigation/modules/inbox/model/chat_model.dart';
import '../../../controller/nav_controller.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/view/chat_view.dart';
import '../controller/home_controller.dart';

class RequestDetailView extends StatelessWidget {

  final HostHomeController homeController=Get.find();

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: Widgets.customAppBar(title: "Trip Request Details"),
      body: Padding(
        padding: PaddingConstants.screenPaddingLess,
        child: Obx(


          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  homeController.selectedRequest.value.user?.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(30),
                          child:Widgets.networkImage(
                            homeController.selectedRequest.value.user?.imageUrl ?? "",
                            height: 60,
                            width: 60,
                          ),
                        )
                      : Image.asset(
                          Assets.profileImg1,
                          height: 60,
                          width: 60,
                        ),
                  Widgets.widthSpaceW3,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textNormal('Traveller',
                          color: ColorConstants.textColor, size: 13),
                      SizedBox(height: 3),
                      Texts.textBold(
                          "${homeController.selectedRequest.value.user?.firstName??""}",
                          fontWeight: FontWeight.w600,
                          size: 15),
                    ],
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: Widgets.buildTextGroup(
                      title: "Countries",
                      value: homeController.selectedRequest.value.country?.join(", ") ?? "N/A",
                      icon: Assets.locationIcon,
                    ),
                  ),
                  Widgets.widthSpaceW3,
                  Expanded(
                    child: Widgets.buildTextGroup(
                      title: "Budget Range",
                      value: "${homeController.selectedRequest.value.budgetMin ?? 0}€- ${homeController.selectedRequest.value.budgetMax ?? 0}€",
                      icon: Assets.dollarIcon,
                    ),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: Widgets.buildTextGroup(
                      title: "Start City",
                      value: homeController.selectedRequest.value.startCity ?? "N/A",
                      icon: Assets.cityIcon,
                    ),
                  ),
                  Widgets.widthSpaceW3,
                  Expanded(
                    child: Widgets.buildTextGroup(
                      title: "End City",
                      value: homeController.selectedRequest.value.endCity ?? "N/A",
                      icon: Assets.cityIcon,
                    ),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Widgets.buildTextGroup(
                      title: "Start Date",
                      value:homeController.selectedRequest.value.startDate??"-",
                      icon: Assets.calendarIcon,
                    ),
                  ),
                  Widgets.widthSpaceW3,
                  Expanded(
                    child: Widgets.buildTextGroup(
                      title: "End Date",
                      value: homeController.selectedRequest.value.endDate??"-",
                      icon: Assets.calendarIcon,
                    ),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [

                  Expanded(
                    child: Widgets.buildTextGroup(
                      title: "Number of people",
                      value: homeController.selectedRequest.value.numberOfAttendees.toString()??"",
                      icon: Assets.profileIcon,
                    ),
                  ), Widgets.widthSpaceW3, Expanded(
                    child: Widgets.buildTextGroup(
                      title: "Services",
                      value: homeController.selectedRequest.value.interests??"",
                      icon: Assets.interestIcon,
                    ),
                  ),
                ],
              ),

              Widgets.heightSpaceH2,
              const Text(
                "Additional Notes" ?? "",
                style: TextStyle(
                    fontSize: 10,
                    color: Colors.black54,
                    fontFamily: "InstrumentSansRegular"),
              ),
              const SizedBox(height: 5),
              ReadMoreText(
    homeController.selectedRequest.value.description ?? "-",
    trimLines: 8,

    trimMode: TrimMode.Line,
    trimCollapsedText: 'Show more',
    trimExpandedText: 'Show less',
    lessStyle: TextStyle(
    fontSize: 13,
    color: Colors.black),
    moreStyle: TextStyle(
    fontSize: 13,
    color:  Colors.black),
    style: TextStyle(
    fontSize: 12, color: Colors.black,)),

              Widgets.heightSpaceH5,
              CustomButton(
                onTap: () {

                  final existingChat =
                  Get.find<HostChatController>()
                      .chats
                      .firstWhereOrNull((chat) =>
                  chat.otherUser?.id ==
                      homeController.selectedRequest.value.user?.id);
                  existingChat == null
                      ? Get.to(() => HostChatView(
                    chatUser: OtherUser(
                      id:   homeController.selectedRequest.value.user?.id,
                      name:   "${homeController.selectedRequest.value.user?.firstName??""} ${homeController.selectedRequest.value.user?.lastName??""}",
                      image:   homeController.selectedRequest.value.user?.imageUrl,
                    ),
                  ))?.then((value) {
                    // Get.find<HostNavController>()
                    //     .changeIndex(1);Get.back();

                  })
                      : Get.to(() => HostChatView(
                    chatId:
                    existingChat.chatId.toString(),
                    chatUser: OtherUser(
                      id:   homeController.selectedRequest.value.user?.id,
                      name:   "${homeController.selectedRequest.value.user?.firstName??""} ${homeController.selectedRequest.value.user?.lastName??""}",

                      image:   homeController.selectedRequest.value.user?.imageUrl,
                    ),
                  ))?.then((value) {
                    // Get.find<HostNavController>()
                    //     .changeIndex(1);Get.back();

                  });
                },
                label: "Chat With Traveler",
                backgroundColor: ColorConstants.primaryColor,
                textColor: Colors.black,
                radius: 50,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
