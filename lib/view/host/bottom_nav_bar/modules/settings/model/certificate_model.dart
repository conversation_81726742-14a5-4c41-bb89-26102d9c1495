import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

class CertificateItem {
  final TextEditingController titleController;
  final RxnString title;
  final Rxn<File> file;
  final RxnString id;
  CertificateItem()
      : titleController = TextEditingController(),
        title = RxnString(),
        file = Rxn<File>(),id = RxnString();
}



