import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/settings/view/change_password.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/settings/view/edit_profile.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/settings/view/notification_setting_view.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/custom_dialog.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../authentication/view/login_view.dart';
import '../../../../../traveller/navigation/modules/settings/view/change_password.dart';
import '../../../../../traveller/navigation/modules/settings/view/edit_profile.dart';
import '../../../../../traveller/navigation/modules/settings/view/notification_setting.dart';
import '../../../../../traveller/navigation/modules/settings/view/profile_webview.dart';
import '../controller/profile_controller.dart';

class HostSettingsView extends StatelessWidget {
  HostSettingsView({super.key});
  HostProfileController profileController = Get.put(HostProfileController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        automaticallyImplyLeading: false,
        title:
            Texts.textMedium("Settings", size: 20, fontWeight: FontWeight.w600),
      ),
      body: Column(
        children: [
          // Profile Section
          Padding(
              padding: PaddingConstants.screenPadding.copyWith(top: 10),
              child: GetBuilder<UserController>(
                  init: UserController(),
                  builder: (controller) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            AdvancedAvatar(
                              animated: true,
                              size: 60,
                              foregroundDecoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.grey,
                                  width: 0.0,
                                ),
                              ),
                              child: Widgets.networkImage(
                                  controller.userModel?.imageUrl ?? "",
                                  width: 100,
                                  height: 100),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Texts.textBlock(
                                    "${controller.userModel?.firstName} ${controller.userModel?.lastName}",
                                    color: Colors.black,
                                    size: 15),
                                SizedBox(
                                  height: 2,
                                ),
                                Texts.textNormal(
                                    controller.userModel?.email ?? "",
                                    size: 12,
                                    color: ColorConstants.textColor,
                                    overflow: TextOverflow.ellipsis),
                              ],
                            ),
                          ],
                        ),
                      ],
                    );
                  })),

          // Settings Options List
          Expanded(
            child: ListView(
              children: [
                InkWell(
                  onTap: () {
                    Get.find<HostProfileController>().initializeData();
                    Get.to(() => HostEditProfile());
                  },
                  child: buildSettingsOption(Assets.profileIcon, "Edit Profile",
                      suffixicon: Icons.arrow_forward_ios_rounded),
                ),
                InkWell(
                  onTap: () {
                    Get.to(() => ChangePassword());
                  },
                  child: buildSettingsOption(Assets.lockIcon, "Change Password",
                      suffixicon: Icons.arrow_forward_ios_rounded),
                ),
                InkWell(
                  onTap: () {
                    Get.to(() => NotificationSettingsScreen());
                  },
                  child: buildSettingsOption(
                      Assets.notificationSettingIcon, "Notifications Settings",
                      suffixicon: Icons.arrow_forward_ios_rounded),
                ),
                InkWell(
                  onTap: () {
                    Get.to(() => const ProfileWebView(
                        url: Endpoints.privacyPolicy, title: "Privacy Policy"));
                  },
                  child: buildSettingsOption(
                      Assets.privacyPolicy, "Privacy Policy",
                      suffixicon: Icons.arrow_forward_ios_rounded),
                ),
                InkWell(
                  onTap: () {
                    Get.to(() => const ProfileWebView(
                        url: Endpoints.privacyPolicy,
                        title: "Terms And Conditions"));
                  },
                  child: buildSettingsOption(
                      Assets.termcondition, "Terms And Conditions",
                      suffixicon: Icons.arrow_forward_ios_rounded),
                ),
                InkWell(
                    onTap: () {
                      logoutDialog(context);
                    },
                    child: buildSettingsOption(Assets.logoutIcon, "Logout")),
             Widgets.heightSpaceH5,   Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: CustomButton(
                    label: "Delete Account",
                    backgroundColor: Colors.red,textColor: Colors.white,
                    onTap: () {
                      deleteAccountDialog(context);
                    },
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSettingsOption(icon, String title,
      {bool isDestructive = false, suffixicon, color}) {
    return Column(
      children: [
        Divider(
          thickness: .7,
          color: ColorConstants.greyColor,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            children: [
              Image.asset(
                icon,
                height: 20,
                width: 20,
              ),
              Widgets.widthSpaceW3,
              Texts.textNormal(title,
                  textAlign: TextAlign.start, size: 13, color: color),
              Expanded(
                child: Container(
                  height: 10,
                ),
              ),
              Icon(
                suffixicon,
                size: 20,
              ),
            ],
          ),
        )
      ],
    );
  }

  logoutDialog(
    BuildContext context,
  ) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            content: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                      margin: EdgeInsets.only(left: 20),
                      alignment: Alignment.topRight,
                      child: InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Icon(CupertinoIcons.clear_circled_solid))),
                  Widgets.heightSpaceH2,
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: ColorConstants.primaryColor,
                    child: Icon(
                      Icons.logout,
                      size: 40,
                    ),
                  ),
                  Widgets.heightSpaceH2,
                  Texts.textBlock(
                    "Logout",
                    size: 22,
                  ),
                  Widgets.heightSpaceH1,
                  Texts.textMedium("Do you really want to logout",
                      size: 14, textAlign: TextAlign.center),
                  Widgets.heightSpaceH3,
                  Padding(
                    padding: PaddingConstants.screenPaddingHalf,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          child: CustomButton(
                            label: "Cancel",
                            borderColor: Colors.transparent,
                            backgroundColor: ColorConstants.silverColor,
                            textColor: ColorConstants.blackColor,
                            radius: 50,
                            onTap: () {
                              Get.back();
                            },
                          ),
                        ),
                        Widgets.widthSpaceW3,
                        Widgets.widthSpaceW1,
                        Expanded(
                          child: CustomButton(
                            label: "Confirm",
                            borderColor: Colors.transparent,
                            backgroundColor: ColorConstants.blackColor,
                            textColor: ColorConstants.whiteColor,
                            radius: 50,
                            onTap: () {
                              profileController.userController
                                  .requestLogoutAccount();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  deleteAccountDialog(
    BuildContext context,
  ) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            content: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                      margin: EdgeInsets.only(left: 20),
                      alignment: Alignment.topRight,
                      child: InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Icon(CupertinoIcons.clear_circled_solid))),
                  Widgets.heightSpaceH2,
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: ColorConstants.primaryColor,
                    child: Icon(
                      Icons.delete,
                      size: 40,
                    ),
                  ),
                  Widgets.heightSpaceH2,
                  Texts.textBlock(
                    "Delete Account",
                    size: 22,
                  ),
                  Widgets.heightSpaceH1,
                  Texts.textMedium("Do you really want to delete this account?",
                      size: 14, textAlign: TextAlign.center),
                  Widgets.heightSpaceH3,
                  Padding(
                    padding: PaddingConstants.screenPaddingHalf,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          child: CustomButton(
                            label: "Cancel",
                            borderColor: Colors.transparent,
                            backgroundColor: ColorConstants.silverColor,
                            textColor: ColorConstants.blackColor,
                            radius: 50,
                            onTap: () {
                              Get.back();
                            },
                          ),
                        ),
                        Widgets.widthSpaceW3,
                        Widgets.widthSpaceW1,
                        Expanded(
                          child: CustomButton(
                            label: "Confirm",
                            borderColor: Colors.transparent,
                            backgroundColor: ColorConstants.blackColor,
                            textColor: ColorConstants.whiteColor,
                            radius: 50,
                            onTap: () {
                              profileController.userController
                                  .requestDeletionAccount();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }
}
