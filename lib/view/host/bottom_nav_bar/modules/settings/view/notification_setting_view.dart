import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/notification_controller.dart';

class NotificationSettingsScreen extends StatelessWidget {
  NotificationSettingsScreen({super.key});

  final controller = Get.put(HostNotificationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "Notifications Settings"),
      body:  SingleChildScrollView(
          child: Padding(
            padding: PaddingConstants.screenPadding.copyWith(top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Receive alerts about booking changes, including new requests, modifications, and cancellations',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.bookingUpdates.value ?? false,
                              onChanged: (v) {
                                controller.bookingUpdates.value = v;controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          "Get updates about payment statuses, including successful bookings and pending transactions",
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                          () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.paymentNotifications.value ?? false,
                              onChanged: (v) {
                                controller.paymentNotifications.value = v;controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Get alerts about upcoming bookings and expiring trip requests',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                          () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.tripAlerts.value ?? false,
                              onChanged: (v) {
                                controller.tripAlerts.value = v;controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Get notified when travelers send you messages',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                          () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.messageNotifications.value ?? false,
                              onChanged: (v) {
                                controller.messageNotifications.value = v;controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Receive instant notifications on your mobile device',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                          () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.pushNotifications.value ?? false,
                              onChanged: (v) {
                                controller.pushNotifications.value = v;controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Receive important updates via email',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                          () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.emailNotifications.value ?? false,
                              onChanged: (v) {
                                controller.emailNotifications.value = v;controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
              ],
            ),
          ),
        )

    );
  }


}
