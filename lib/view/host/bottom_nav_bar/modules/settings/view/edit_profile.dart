import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/utils/extensions.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:intl/intl.dart';

import '../../../../../../core/constants/constants_list.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/custom_dropdown.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/select_city_view.dart';
import '../../../../../../core/widgets/user_review.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/profile_controller.dart';

class HostEditProfile extends StatelessWidget {
  final HostProfileController controller = Get.find();

  HostEditProfile({super.key});

  void showMultiSelectBottomSheet(BuildContext context, String title,
      List<String> items, RxList<String> selectedItems) {
    final searchController = TextEditingController();
    final RxList<String> filteredItems = RxList<String>(items);

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true, // Add this to make bottom sheet larger
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Container(
          padding: const EdgeInsets.all(20),
          // Set height to 90% of screen height
          height: MediaQuery.of(context).size.height * 0.9,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock(
                    "Select $title",
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const SizedBox(height: 10),

              // Search Field
              EntrySearchField(
                controller: searchController,
                prefixIcon: Assets.searchIcon,
                hint: "Search $title",
                onChange: (value) {
                  if (value != null) {
                    filteredItems.value = items
                        .where((item) =>
                            item.toLowerCase().contains(value.toLowerCase()))
                        .toList();
                  } else {
                    filteredItems.value = items;
                  }
                },
              ),

              const SizedBox(height: 10),

              Expanded(
                child: Obx(() => ListView.separated(
                      itemCount: filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = filteredItems[index];
                        return Obx(() => CheckboxListTile(
                              contentPadding: EdgeInsets.zero,
                              title: Text(item),
                              value: selectedItems.contains(item),
                              onChanged: (bool? value) {
                                if (value == true) {
                                  selectedItems.add(item);
                                } else {
                                  selectedItems.remove(item);
                                }
                              },
                            ));
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return Divider(
                            color: ColorConstants.greyColor, thickness: .5);
                      },
                    )),
              ),

              CustomButton(
                label: "Done",
                textColor: ColorConstants.blackColor,
                backgroundColor: ColorConstants.primaryColor,
                radius: 50,
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void showGenderBottomSheet(BuildContext context) {
    final List<String> genders = [
      'Male',
      'Female',
      'Other',
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBlock(
                  "Select Gender",
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 10),
            ...genders
                .map((gender) => Column(
                      children: [
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          title: Text(gender),
                          onTap: () {
                            controller.selectedGender.value = gender;
                            Get.back();
                          },
                          trailing: Obx(() =>
                              controller.selectedGender.value == gender
                                  ? Icon(Icons.check,
                                      color: ColorConstants.primaryColor)
                                  : const SizedBox()),
                        ),
                        Divider(color: ColorConstants.greyColor, thickness: .5)
                      ],
                    ))
                .toList(),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () => context.hideKeyboard(),
        child: SingleChildScrollView(
          child: Obx(
            () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  alignment: Alignment.topCenter,
                  clipBehavior: Clip.none,
                  children: [Container(height:
                      .28.sh,width: double.infinity),
                    controller.hostDetail.value.hostProfile?.bannerUrl != null
                        ? Widgets.networkImage(
                            controller
                                    .hostDetail.value.hostProfile?.bannerUrl ??
                                "",
                            height:
                                .2.sh,
                            width: double.infinity,
                          )
                        : Image.asset(
                            Assets.coverImg,
                            fit: BoxFit
                                .cover, // Ensure the image covers the area
                            height:
                                .2.sh, // Set a fixed height for the cover image
                            width: double.infinity,
                          ),
                    Positioned(
                        top: 50,
                        left: 20,
                        child: GestureDetector(
                            onTap: () {
                              Get.back();
                            },
                            child: const Icon(
                              Icons.arrow_back_ios_new_outlined,
                              size: 20,
                              color: Colors.white,
                            ))),
                    Positioned(
                      bottom: .01.sh,
                      child: GestureDetector(    behavior: HitTestBehavior.translucent,

                        onTap: () {
                          controller.showImageSourceDialog(context,
                              isProfile: true);
                        },
                        child: Stack(
                          alignment: Alignment.bottomRight,
                          clipBehavior: Clip.none,
                          children: [
                            AdvancedAvatar(
                              animated: true,
                              size: 120,
                              foregroundDecoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.grey,
                                  width: 0.0,
                                ),
                              ),
                              child: Widgets.networkImage(
                                  controller
                                          .userController.userModel?.imageUrl ??
                                      "",
                                  width: 200,
                                  height: 200),
                            ),
                            Positioned(
                              right: 10,
                              child: GestureDetector(
                                onTap: () {
                                  controller.showImageSourceDialog(context,
                                      isProfile: true);
                                },
                                child: CircleAvatar(
                                  radius: 15,
                                  backgroundColor: ColorConstants.primaryColor,
                                  child: const Icon(
                                    Icons.camera_alt,
                                    size: 15,
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: .06.sh,
                      right: 20,
                      child: InkWell(
                        onTap: () {
                          controller.showImageSourceDialog(context,
                              isCover: true);
                        },
                        child: CircleAvatar(
                          radius: 15,
                          backgroundColor: ColorConstants.primaryColor,
                          child: const Icon(
                            Icons.camera_alt,
                            size: 15,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),


                Padding(
                  padding: PaddingConstants.screenPadding.copyWith(top: 15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock("Personal Information", size: 16),
                      Widgets.heightSpaceH2,
                      EntryField(
                        readOnly: true,
                        label: "Email", hint: "write here",
                        controller: controller.emailController,
                        // prefixIcon: Assets.mailIcon,
                      ),
                      EntryField(
                        controller: controller.firstNameController,
                        label: "First Name", hint: "write here",

                        // prefixIcon:.mailIcon,
                      ),
                      EntryField(
                        controller: controller.lastNameController,
                        label: "Last Name", hint: "write here",

                        // prefixIcon: Assets.mailIcon,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child:EntryField(
                                  readOnly: true,
                                  onTap: () => controller
                                      .pickCountryCodeBottomSheet(context),
                                  controller: controller.countryCode,
                                  label: "Code",
                                  hint: "+1",
                                  textInputType: TextInputType.phone,
                                ),
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            flex: 8,
                            child: EntryField(
                              controller: controller.phoneController,
                              label: "Phone Number",
                              hint: "write here",
                              textInputType: TextInputType.phone,
                              autoFillType: const [
                                AutofillHints.telephoneNumber
                              ],
                            ),
                          ),
                        ],
                      ),
                      CustomDropdown2(
                          onTap: () async {
                            final DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: controller.selectedDOB.value != ""
                                  ? DateFormat('dd/MM/yyyy')
                                      .parse(controller.selectedDOB.value)
                                  : DateTime(2000),
                              firstDate: DateTime(1900),
                              lastDate: DateTime.now(),
                              builder: (context, child) {
                                return Theme(
                                  data: Theme.of(context).copyWith(
                                    colorScheme: ColorScheme.light(
                                      primary: ColorConstants.primaryColor,
                                    ),
                                  ),
                                  child: child!,
                                );
                              },
                            );

                            if (picked != null) {
                              controller.selectedDOB.value =
                                  DateFormat('dd/MM/yyyy').format(picked);
                            }
                          },
                          value: controller.selectedDOB.value == ""
                              ? null
                              : controller.selectedDOB.value,
                          hint: "dd/mm/yyyy",
                          label: "Date of Birth"),
                      EntryBigField(
                        controller: controller.bioController,
                        maxLines: null,
                        textCapitalization: TextCapitalization.sentences,
                        minLines: 7,
                        label: "Bio",
                        hint: "Write here",
                      ),
                      EntryField(
                        controller: controller.socialMediaLink,
                        label: "Social Media Link", hint: "Enter url",
                        // prefixIcon: Assets.mailIcon,
                      ),
                      Widgets.heightSpaceH1,
                      Obx(
                        () => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Texts.textBlock("Video or Youtube Link", size: 16),
                            controller.hostDetail.value.hostProfile?.videoInterviewUrl ==null
                                    ? const SizedBox()
                                : Row(
                                    children: [
                                     controller
                                                      .hostDetail
                                                      .value
                                                      .hostProfile!
                                                      .videoInterviewUrl
                                                      !.contains(
                                                          'youtube.com')  ||
                                                      controller
                                                          .hostDetail
                                                          .value
                                                          .hostProfile!
                                                          .videoInterviewUrl!
                                                          .contains('youtu.be')
                                      ?InkWell(
                                        child: const Icon(
                                          Icons.delete,
                                          color: Colors.red,
                                          size: 17,
                                        ),
                                        onTap: () {
                                          if (controller.selectedVideo.value !=
                                              null) {
                                            controller.selectedVideo.value =
                                            null;
                                          } else {
                                            controller.deleteVideoUrl();
                                          }
                                        },
                                      ):
                                        Row(
                                          children: [
                                            InkWell(
                                              child: const Icon(
                                                Icons.edit,
                                                color: Colors.black,
                                                size: 17,
                                              ),
                                              onTap: () => controller.pickVideo(),
                                            ),
                                                                                  SizedBox(width: 10),
                                                                                  InkWell(
                                            child: const Icon(
                                              Icons.delete,
                                              color: Colors.red,
                                              size: 17,
                                            ),
                                            onTap: () {
                                              if (controller.selectedVideo.value !=
                                                  null) {
                                                controller.selectedVideo.value =
                                                    null;
                                              } else {
                                                controller.deleteVideoUrl();
                                              }
                                            },
                                                                                  ),
                                          ],
                                        ),
                                    ],
                                  )

                          ],
                        ),
                      ),
                      Widgets.heightSpaceH1,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Obx(() {
                            final hasVideoFile =
                                controller.selectedVideo.value != null;
                            final existingVideoUrl = controller.hostDetail.value
                                .hostProfile?.videoInterviewUrl;
                            final existingThumbnailUrl = controller
                                .hostDetail.value.hostProfile?.videoThumbnail;
                            final isYoutubeLink = existingVideoUrl != null
                                ? existingVideoUrl!.contains('youtube.com') ||
                                        existingVideoUrl.contains('youtu.be')
                                : false;

                            return Column(
                              children: [
                                // Show upload options when no video exists
                                if (!hasVideoFile && existingVideoUrl == null)
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomButton(
                                          backgroundColor:
                                              ColorConstants.lightOrange,
                                          borderColor: ColorConstants.splash,
                                          onTap: () => controller.pickVideo(),
                                          icon: Icon(
                                              CupertinoIcons.cloud_upload,
                                              size: 18,
                                              color: ColorConstants.splash),
                                          label: "Video",
                                          textColor: ColorConstants.splash,
                                          fontSize: 12,
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Expanded(
                                        child: CustomButton(
                                          backgroundColor:
                                              ColorConstants.lightOrange,
                                          borderColor: ColorConstants.splash,
                                          onTap: () => controller
                                              .showYoutubeLinkDialog(context),
                                          icon: Icon(Icons.add_link_sharp,
                                              size: 18,
                                              color: ColorConstants.splash),
                                          label: "Youtube Link",
                                          textColor: ColorConstants.splash,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),

                                // Show video preview
                                if (hasVideoFile || existingVideoUrl != null)
                                  Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // Video thumbnail or preview
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Container(
                                          height: 140,
                                          width: 1.sw,
                                          decoration: BoxDecoration(
                                            color: Colors.black12,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: hasVideoFile
                                              ? FutureBuilder<String?>(
                                                  future: controller
                                                      .getVideoThumbnail(
                                                          controller
                                                              .selectedVideo
                                                              .value!
                                                              .path),
                                                  builder: (context, snapshot) {
                                                    if (snapshot.hasData) {
                                                      return Image.file(
                                                        File(snapshot.data!),
                                                        fit: BoxFit.cover,
                                                      );
                                                    }
                                                    return const Center(
                                                      child:
                                                          CircularProgressIndicator(),
                                                    );
                                                  },
                                                )
                                              : isYoutubeLink
                                                  ? Stack(
                                                      alignment:
                                                          Alignment.center,
                                                      children: [
                                                        // YouTube preview (you might want to show YouTube thumbnail here)
                                                        Container(
                                                          color: Colors.black87,
                                                          child: Center(
                                                            child: Icon(
                                                              CupertinoIcons
                                                                  .play_rectangle,
                                                              size: 40,
                                                              color:
                                                                  ColorConstants
                                                                      .splash,
                                                            ),
                                                          ),
                                                        ),
                                                        Text(
                                                          'YouTube Video',
                                                          style: TextStyle(
                                                            color:
                                                                ColorConstants
                                                                    .splash,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                  : existingThumbnailUrl != null
                                                      ? Widgets.networkImage(
                                                          existingThumbnailUrl,
                                                        )
                                                      : Icon(
                                                          Icons.video_file,
                                                          color: ColorConstants
                                                              .splash,
                                                          size: 40,
                                                        ),
                                        ),
                                      ),

                                      // Play button overlay
                                      CircleAvatar(
                                        backgroundColor: ColorConstants.splash
                                            .withOpacity(0.8),
                                        radius: 20,
                                        child: IconButton(
                                          icon: const Icon(
                                            CupertinoIcons.play_fill,
                                            color: Colors.white,
                                          ),
                                          onPressed: () {
                                            // if (hasVideoFile) {
                                            //   controller.playVideo(controller.selectedVideo.value!.path);
                                            // } else if (isYoutubeLink) {
                                            //   controller.playYoutubeVideo(existingVideoUrl!);
                                            // } else if (existingVideoUrl != null) {
                                            //   controller.playVideo(existingVideoUrl);
                                            // }
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                              ],
                            );
                          }),
                        ],
                      ),
                      Widgets.heightSpaceH2,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Texts.textBlock(
                            "Passport Photo",
                            size: 16,
                          ),
                          if (controller.hostDetail.value.hostProfile
                                  ?.passportIdCardUrl !=
                              null)
                            InkWell(
                              child: const Icon(
                                Icons.edit,
                                color: Colors.black,
                                size: 17,
                              ),
                              onTap: () => controller.showImageSourceDialog(
                                context,
                                isPassport: true,
                              ),
                            ),
                        ],
                      ),
                      Widgets.heightSpaceH1,
                      controller.hostDetail.value.hostProfile
                                  ?.passportIdCardUrl !=
                              null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Widgets.networkImage(
                                controller.hostDetail.value.hostProfile
                                        ?.passportIdCardUrl ??
                                    "",
                                height: 150,
                                width: double.infinity,
                              ),
                            )
                          : InkWell(
                              onTap: () => controller.showImageSourceDialog(
                                context,
                                isPassport: true,
                              ),
                              child: Container(
                                height: 150,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: ColorConstants.lightOrange,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    width: .5,
                                    color: ColorConstants.splash,
                                    style: BorderStyle.solid,
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add_photo_alternate_outlined,
                                      size: 40,
                                      color: ColorConstants.splash,
                                    ),
                                    Widgets.heightSpaceH1,
                                    Texts.textBlock(
                                      "Upload Passport Photo",
                                      size: 12,
                                      color: ColorConstants.splash,
                                    ),
                                    Widgets.heightSpaceH1,
                                    Texts.textNormal(
                                      "(Front Side)",
                                      size: 12,
                                      color: ColorConstants.splash,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                      Widgets.heightSpaceH2,
                      Texts.textBlock("General Information", size: 16),
                      Widgets.heightSpaceH1,
                      CustomDropdown2(
                          onTap: () {
                            controller.pickCountryBottomSheet(context);
                          },
                          value: controller.selectedCountry.value == ""
                              ? null
                              : controller.selectedCountry.value,
                          hint: "--Select--",
                          label: "Country"),
                      CustomDropdown2(
                          onTap: () {
                            if (controller.selectedCountry.value == "") {
                              Widgets.showSnackBar(
                                  "Incomplete Form", "Please select country.");
                            } else {
                              Get.to(SelectCityListView(
                                countries: [controller.selectedCountry.value],
                              ))?.then((result) {
                                if (result != null) {
                                  print(result);
                                  controller.selectedCity.value =
                                      result['city'];
                                }
                              });
                            }
                          },
                          value: controller.selectedCity.value != ""
                              ? controller.selectedCity.value
                              : null,
                          hint: "--Select--",
                          label: "Start City"),
                      CustomDropdown2(
                          onTap: () {
                            context.hideKeyboard();
                            showGenderBottomSheet(context);
                          },
                          value: controller.selectedGender.value.isEmpty
                              ? null
                              : controller.selectedGender.value,
                          hint: "Select here",
                          label: "Gender"),
                      Obx(() => CustomDropdown2(
                            onTap: () => showMultiSelectBottomSheet(
                              context,
                              "Languages",
                              Data.languages,
                              controller.selectedLanguages,
                            ),
                            value: controller.selectedLanguages.isEmpty
                                ? null
                                : controller.selectedLanguages.join(", "),
                            hint: "Select languages",
                            label: "Languages",
                          )),
                      Widgets.heightSpaceH1,
                      Texts.textBlock("Offered Certificates", size: 16),
                      Widgets.heightSpaceH1,
                      Obx(() => CustomDropdown2(
                            onTap: () => showMultiSelectBottomSheet(
                              context,
                              "Countries",
                              Data.countries,
                              controller.selectedOfferedCountries,
                            ),
                            value: controller.selectedOfferedCountries.isEmpty
                                ? null
                                : controller.selectedOfferedCountries
                                    .join(", "),
                            hint: "Select countries",
                            label: "Countries",
                          )),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Texts.textBlock("Certificates And Trainings",
                              size: 16),
                          IconButton(
                            icon: const Icon(Icons.add),
                            onPressed: () {
                              controller.addNewCertificate();
                            },
                          )
                        ],
                      ),
                      Widgets.heightSpaceH1,
                      Obx(() => Column(
                            children: [
                              for (int i = 0;
                                  i < controller.certificates.length;
                                  i++)
                                Container(
                                  margin:
                                      const EdgeInsets.only(bottom: 10.0),
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Colors.grey.shade200),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      EntryField(
                                        controller: controller
                                            .certificates[i]
                                            .titleController,
                                        hint: "Enter certificate title",
                                      ),
                                      Obx(
                                        () => controller.certificates[i]
                                                    .file.value !=
                                                null
                                            ? Container(
                                                padding: const EdgeInsets
                                                    .symmetric(
                                                  horizontal: 12,
                                                  vertical: 8,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: ColorConstants
                                                      .lightOrange,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          8),
                                                ),
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons.file_present,
                                                      size: 20,
                                                      color: ColorConstants
                                                          .splash,
                                                    ),
                                                    const SizedBox(
                                                        width: 8),
                                                    Expanded(
                                                      child: Text(
                                                        controller
                                                            .certificates[i]
                                                            .file
                                                            .value!
                                                            .path
                                                            .split('/')
                                                            .last,
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color:
                                                              ColorConstants
                                                                  .splash,
                                                        ),
                                                        maxLines: 1,
                                                        overflow:
                                                            TextOverflow
                                                                .ellipsis,
                                                      ),
                                                    ),
                                                    InkWell(
                                                      child: Icon(
                                                        CupertinoIcons
                                                            .delete,
                                                        size: 17,
                                                        color: Colors.red,
                                                      ),
                                                      onTap: () {
                                                        controller
                                                          .deleteCertificate(controller.certificates[i].id.value.toString());
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : InkWell(
                                                onTap: () {

                                                  if(controller
                                                      .certificates[i]
                                                      .titleController.text.isEmpty){
                                                    Widgets.showSnackBar("Incomplete Form", "Please enter certificate title");

                                                  }else{
                                                  controller
                                                    .pickCertificateFile(i);}
                                                },
                                                child: Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 10),
                                                  decoration: BoxDecoration(
                                                    color: ColorConstants
                                                        .lightOrange,
                                                    borderRadius:
                                                        BorderRadius
                                                            .circular(8),
                                                    border: Border.all(
                                                      color: ColorConstants
                                                          .splash,
                                                      width: 0.5,
                                                    ),
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Icon(
                                                        CupertinoIcons
                                                            .cloud_upload,
                                                        color:
                                                            ColorConstants
                                                                .splash,
                                                        size: 18,
                                                      ),
                                                      const SizedBox(
                                                          width: 8),
                                                      Text(
                                                        "Upload Certificate",
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color:
                                                              ColorConstants
                                                                  .splash,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          )),
                      Widgets.heightSpaceH2,
                      CustomButton(
                        label: "Save Changes",
                        textColor: ColorConstants.blackColor,
                        backgroundColor: ColorConstants.primaryColor,
                        radius: 50,
                        onTap: () {
                          controller.updateProfile();
                        },
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
