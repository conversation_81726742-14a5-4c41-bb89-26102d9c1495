import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import '../../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../../core/widgets/widgets.dart';
import '../../../../../../core/services/http_service.dart';

class HostNotificationController extends GetxController {
  final RxBool isLoading = false.obs;
  final RxBool bookingUpdates = false.obs;
  final RxBool paymentNotifications = false.obs;
  final RxBool tripAlerts = false.obs;
  final RxBool messageNotifications = false.obs;
  final RxBool pushNotifications = false.obs;
  final RxBool emailNotifications = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchNotificationSettings();
  }

  Future<void> fetchNotificationSettings() async {
    isLoading.value = true;
    try {
      final response = await ApiService.getData(Endpoints.notificationSettings);

      if (response.status == true) {
        final settings = response.data['notification_settings'];

        bookingUpdates.value  = settings['booking_updates'] == 1 ? true : false;
        paymentNotifications.value  = settings['payment_notifications'] == 1 ? true : false;;
        tripAlerts.value  = settings['trip_alerts'] == 1 ? true : false;
        messageNotifications.value  = settings['message_notifications'] == 1 ? true : false;
        pushNotifications.value  = settings['push_notifications'] == 1 ? true : false;
        emailNotifications.value = settings['email_notifications'] == 1 ? true : false;

      } else {
        Widgets.showSnackBar('Error', response.message ?? 'Failed to load settings');
      }
    } catch (e) {
      Widgets.showSnackBar('Error', 'Failed to load notification settings');
    } finally {
      isLoading.value = false;
    }
  }

  updateNotificationSetting() async {
    try {
      var data = {
        'booking_updates':bookingUpdates.value==true?1:0,
        'payment_notifications':paymentNotifications.value==true?1:0,
        'trip_alerts':tripAlerts.value==true?1:0,
        'message_notifications':messageNotifications.value==true?1:0,
        'push_notifications':pushNotifications.value==true?1:0,
        'email_notifications':emailNotifications.value==true?1:0,
      };
      var response =
      await ApiService.postData(Endpoints.updateNotificationSettings, data);


      if (response.status == true) {
      } else {
      }
    } catch (e) {
    } finally {
      // fetchUserDetails();
    }
  }



}