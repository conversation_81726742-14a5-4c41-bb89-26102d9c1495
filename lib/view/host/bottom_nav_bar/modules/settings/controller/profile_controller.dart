import 'dart:convert';
import 'dart:io';

import 'package:country_picker/country_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:indyguide/controller/user_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/model/booking_detail_model.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_compress/video_compress.dart';

import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/constants/padding_constants.dart';
import '../../../../../../core/services/http_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../../model/user_model.dart';
import 'package:http/http.dart' as http;

import '../../../../../traveller/navigation/modules/hosts/model/host_detail_model.dart' as host;
import '../../../../../traveller/navigation/modules/hosts/model/host_detail_model.dart' as rating;
import '../model/certificate_model.dart';

class HostProfileController extends GetxController {
  String? imagePath;
  String? coverImagePath;
  String? passportImagePath;
  final RxList<CertificateItem> certificates = <CertificateItem>[].obs;

  late UserController userController;
  RxList reviews = <host.UserRatings>[].obs;
  RxBool isReviewLoading = false.obs;
  RxBool isReviewMoreLoading = false.obs;
  RxInt totalReviews = 0.obs;
  RxInt currentReviewPage = 0.obs;
  TextEditingController firstNameController = TextEditingController();
  TextEditingController countryCode = TextEditingController();

  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController socialMediaLink = TextEditingController();
  TextEditingController bioController = TextEditingController();
  TextEditingController languagesController = TextEditingController();
  RxString selectedCountry = "".obs;
  RxString selectedDOB = "".obs;
  RxString selectedCity = "".obs;
  RxBool isHostDetailLoading = false.obs;
  var hostDetail = host.HostDetail().obs;
  RxList<String> selectedLanguages = <String>[].obs;
  RxList<String> selectedOfferedCountries = <String>[].obs;
  final RxString selectedGender = ''.obs;
  RxBool showPublicView = false.obs;

  final videoLinkController = TextEditingController();
  final Rx<File?> selectedVideo = Rx<File?>(null);
  final ImagePicker _picker = ImagePicker();
  RxString selectedCountryCode = "1".obs;

  @override
  void onInit() {
    super.onInit();
    userController = Get.find();
    fetchHostDetail();
  }

  void initializeData() {
    emailController.text = userController.userModel?.email ?? "";
    firstNameController.text = userController.userModel?.firstName ?? "";
    lastNameController.text = userController.userModel?.lastName ?? "";
    phoneController.text = hostDetail.value.hostProfile?.phone ?? "";
    socialMediaLink.text = userController.userModel?.instagramLink ?? "";
    bioController.text = hostDetail.value.hostProfile?.bio ?? "";
    selectedDOB.value = hostDetail.value.hostProfile?.dob ?? "";
    selectedCity.value = hostDetail.value.hostProfile?.city ?? "";
    selectedGender.value = hostDetail.value.hostProfile?.gender ?? "";
    videoLinkController.text =
        hostDetail.value.hostProfile?.videoInterviewUrl ?? "";
   countryCode.text = hostDetail.value.countryCode ?? "";

    if (hostDetail.value.hostProfile?.languages != null) {
      selectedLanguages.value =
          hostDetail.value.hostProfile!.languages!.cast<String>();
    }
    if (hostDetail.value.hostProfile?.offeredCountries != null) {
      selectedOfferedCountries.value =
          hostDetail.value.hostProfile!.offeredCountries!.cast<String>();
    }

    selectedCountry.value = hostDetail.value.hostProfile?.country ?? "";
    certificates.clear();
    // Initialize certificates
    if (hostDetail
            .value.hostProfile?.certificateOfIncorporationUrl?.isNotEmpty ??
        false) {
      // Clear existing controllers first
      for (int i = 0;
          i <
              hostDetail
                  .value.hostProfile!.certificateOfIncorporationUrl!.length;
          i++) {
        certificates.add(CertificateItem());
        certificates[i].id.value = hostDetail.value.hostProfile!
                .certificateOfIncorporationUrl![i].id.toString() ??
            "";  certificates[i].titleController.text = hostDetail.value.hostProfile!
            .certificateOfIncorporationUrl![i].certificateName ??
            "";
        certificates[i].file.value = File(hostDetail.value.hostProfile!
                .certificateOfIncorporationUrl![i].certificateUrl ??
            "");
      }
    }
  }

  pickCountryCodeBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(
          searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "Search Country")),
      showPhoneCode: true, // Set to true if you need phone codes
      onSelect: (Country country) {
        print(country.phoneCode);
        selectedCountryCode.value = country.phoneCode;countryCode.text = country.phoneCode;
      },
    );
  }

  Future<void> updateProfile() async {
    try {
      if (firstNameController.text.isEmpty) {
        Widgets.showSnackBar("Alert", "First name is required");
        return;
      }
      if (lastNameController.text.isEmpty) {
        Widgets.showSnackBar("Alert", "Last name is required");
        return;
      }

      Widgets.showLoader("Saving Profile...");

      final data = {
        'email': emailController.text,
        'first_name': firstNameController.text,
        "last_name": lastNameController.text,
        'phone': phoneController.text,
        'instagram_link': socialMediaLink.text,
        "video_interview": videoLinkController.text,
        'country': selectedCountry.value,
        'bio': bioController.text,
        'dob': selectedDOB.value,"country_code":countryCode.text,
        "offered_countries": selectedOfferedCountries.toList(),
        'city': selectedCity.value,
        'gender': selectedGender.value,
        "certificate_of_incorporation": "",
        'languages': selectedLanguages.toList(),
      };

      final response = await ApiService.postData(Endpoints.updateProfile, data);

      if (response.status == true) {
        final userModel = UserModel.fromJson(response.data['user']);
        await userController.saveUser(userModel, userController.token ?? "",false);
        await userController.fetchUser();
        fetchHostDetail();
        Get.back();
        Widgets.showSnackBar("Success", "Profile updated successfully");
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Update failed");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  Future<void> fetchHostDetail() async {
    try {
      isHostDetailLoading.value = true;
      var response = await ApiService.getData(
          "${Endpoints.hostProfile}/${userController.userModel?.id}");
      if (response.status == true) {
        hostDetail.value = host.HostDetail.fromJson(response.data['host']);
        initializeData();
      }
    } catch (e) {
      print(e);
    } finally {
      isHostDetailLoading.value = false;
    }
  }

  changePassword(String password, String oldPassword) async {
    Widgets.showLoader("Loading..");

    try {
      var payload = {"new_password": password, "old_password": oldPassword};
      var response =
          await ApiService.postData(Endpoints.updatePassword, payload);

      Widgets.hideLoader();
      if (response.status == true) {
        Get.back();
        Widgets.showSnackBar("Success", response.message.toString());
      } else {
        Widgets.showSnackBar("Error", response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", e.toString());
    }
  }
  deleteCertificate(String id) async {
    Widgets.showLoader("Deleting certificate");

    try {
      var payload = {"certificate_id": id};
      var response =
      await ApiService.postData(Endpoints.deleteCertificate, payload);

      Widgets.hideLoader();
      if (response.status == true) {
        fetchHostDetail();
      } else {
      }
    } catch (e) {
      Widgets.hideLoader();

    }
  }
  pickCountryBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(
          searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "Search Country")),
      showPhoneCode: false, // Set to true if you need phone codes
      onSelect: (Country country) {
        selectedCountry.value = country.name;
      },
    );
  }

  void showImageSourceDialog(BuildContext context,
      {bool isProfile = false, bool isCover = false, bool isPassport = false}) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock("Select an action",
                      align: TextAlign.center),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: const Icon(Icons.clear, color: Colors.black54),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.camera,
                      isProfile: isProfile,
                      isCover: isCover,
                      isPassport: isPassport);
                },
              ),
              Divider(color: ColorConstants.greyColor, thickness: .5),
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.gallery,
                      isProfile: isProfile,
                      isCover: isCover,
                      isPassport: isPassport);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> pickImage(ImageSource source,
      {bool isProfile = false,
      bool isCover = false,
      bool isPassport = false}) async {
    try {
      final XFile? file = await ImagePicker().pickImage(source: source);
      if (file != null) {
        // Crop the image
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: file.path,
          aspectRatio: isPassport
              ?  const CropAspectRatio(ratioX: 16, ratioY: 9)
              : isProfile
                  ? const CropAspectRatio(ratioX: 1, ratioY: 1)
                  : const CropAspectRatio(ratioX: 16, ratioY: 9),
          compressQuality: 70,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: 'Adjust Image',
              toolbarColor: ColorConstants.primaryColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: isPassport
                  ? CropAspectRatioPreset.ratio16x9
                  : isProfile
                      ? CropAspectRatioPreset.square
                      : CropAspectRatioPreset.ratio16x9,
              lockAspectRatio: true,
            ),
          ],
        );

        if (croppedFile != null) {
          if (isPassport) {
            passportImagePath = croppedFile.path;
            await uploadPassportPhoto();
          } else if (isProfile) {
            imagePath = croppedFile.path;
            await changeProfilePic();
          } else if (isCover) {
            coverImagePath = croppedFile.path;
             await changeBannerPic();
          }
          update();
        }
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  changeProfilePic() async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateProfileImage}'));
    var pic = await http.MultipartFile.fromPath('image', imagePath ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {
        UserModel userModel = UserModel.fromJson(decodedData['user']);
       await userController.saveUser(userModel, userController.token ?? "",false);
        userController.fetchUser();
        Widgets.showSnackBar("Success", "Image Updated Successfully");
        fetchHostDetail();
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }

  changeBannerPic() async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateBannerImage}'));
    var pic =
        await http.MultipartFile.fromPath('banner_image', coverImagePath ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {
        fetchHostDetail();
        Widgets.showSnackBar("Success", "Banner Updated Successfully");
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }

  Future<void> pickVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 10),
      );

      if (video != null) {
        // Check file size (100MB limit)
        final file = File(video.path);
        final sizeInBytes = await file.length();
        final sizeInMB = sizeInBytes / (1024 * 1024);

        if (sizeInMB > 100) {
          Widgets.showSnackBar(
            "Error",
            "Video size should be less than 100MB",
          );
          return;
        }


        try {


          // Generate thumbnail
          final thumbnail = await VideoCompress.getFileThumbnail(
            video.path,
            quality: 50,
            position: -1, // -1 means the middle of the video
          );

          selectedVideo.value = File(video.path);
          videoLinkController.clear();

          Widgets.hideLoader();
          await changeVideoProfileWithThumbnail(thumbnail);
        } catch (e) {
          Widgets.hideLoader();
          Widgets.showSnackBar("Error", "Failed to compress video: $e");
          return;
        }
      }
    } catch (e) {
      Widgets.showSnackBar(
        "Error",
        "Failed to pick video: $e",
      );
    }
  }

  Future<void> changeVideoProfileWithThumbnail(File thumbnail) async {
    Widgets.showLoader("Uploading Video..");
    try {
      var request = http.MultipartRequest(
          'POST', Uri.parse('${Endpoints.baseURL}${Endpoints.updateProfile}'));

      var videoFile = await http.MultipartFile.fromPath(
          'video_interview', selectedVideo.value?.path ?? "");
      request.files.add(videoFile);

      var thumbnailFile =
          await http.MultipartFile.fromPath('video_thumbnail', thumbnail.path);
      request.files.add(thumbnailFile);

      request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

      var response = await request.send();
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      Widgets.hideLoader();

      if (response.statusCode == 200 && decodedData['status'] == true) {
        fetchHostDetail();
        Widgets.showSnackBar("Success", "Video Updated Successfully");
      } else {
        Widgets.showSnackBar(
            "Error", decodedData['message'] ?? "Something went wrong");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "Failed to update video: $e");
    }
  }
  Future<void> saveCertificate(File file,String title) async {
    Widgets.showLoader("Uploading Certificate");
    try {
      var request = http.MultipartRequest(
          'POST', Uri.parse('${Endpoints.baseURL}${Endpoints.saveCertificate}'));

      var videoFile = await http.MultipartFile.fromPath(
          'certificate', file.path ?? "");
      request.files.add(videoFile);

      request.headers['Accept'] = 'application/json; charset=UTF-8';

      request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';
       request.fields['certificate_name'] = title;
      var response = await request.send();
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      Widgets.hideLoader();

      if (response.statusCode == 200 && decodedData['status'] == true) {

        fetchHostDetail();        Widgets.showSnackBar("Success", "Certificate Uploaded Successfully");


      } else {
        Widgets.showSnackBar(
            "Error", decodedData['message'] ?? "Something went wrong");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "Failed to update video: $e");
    }
  }

  Future<String?> getVideoThumbnail(String videoPath) async {
    try {
      final thumbnail = await VideoCompress.getFileThumbnail(
        videoPath,
        quality: 50,
        position: -1,
      );
      return thumbnail.path;
    } catch (e) {
      print('Error generating thumbnail: $e');
      return null;
    }
  }

  deleteVideoUrl() async {
    Widgets.showLoader("Loading..");

    try {
      var response = await ApiService.getData(
        Endpoints.deleteVideo,
      );

      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", "Video Deleted Successfully");
        fetchHostDetail();
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Invalid Code");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }


  void clearExistingVideo() {
    if (hostDetail.value.hostProfile?.videoInterviewUrl != null) {
      // hostDetail.update((val) {
      //   val?.videoUrl = null;
      //   val?.videoThumbnailUrl = null;
      // });
      // update();
    } else {}
  }

  void addNewCertificate() {
    certificates.add(CertificateItem());
  }

  void removeCertificate(int index) {
    certificates[index].titleController.dispose();
    certificates.removeAt(index);
  }

  void removeCertificateFile(int index) {
    certificates[index].file.value = null;
  }

  Future<void> pickCertificateFile(int index) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
      );

      if (result != null && result.files.single.path != null) {
        File file = File(result.files.single.path!);

        // Check file size (max 10MB)
        final sizeInBytes = await file.length();
        final sizeInMB = sizeInBytes / (1024 * 1024);


        // If it's an image, compress it
        if (['jpg', 'jpeg', 'png']
            .contains(result.files.single.extension?.toLowerCase())) {
          final directory = await getTemporaryDirectory();
          String newPath =
              '${directory.path}/certificate${Utils.generateUniqueNumber()}.jpg';

          var compressedFile = await FlutterImageCompress.compressAndGetFile(
            file.path,
            newPath,
            quality: 70,
          );

          if (compressedFile != null) {
            file = File(compressedFile.path);
          }
        }

      saveCertificate(file,certificates[index].titleController.text);

      }
    } catch (e) {
      Widgets.showSnackBar("Error", "Failed to pick file");
    }
  }

  void showYoutubeLinkDialog(BuildContext context) {
    final TextEditingController linkController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          content: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(left: 20),
                  alignment: Alignment.topRight,
                  child: InkWell(
                    onTap: () => Get.back(),
                    child: const Icon(CupertinoIcons.clear_circled_solid),
                  ),
                ),
                Widgets.heightSpaceH2,
                CircleAvatar(
                  radius: 40,
                  backgroundColor: ColorConstants.primaryColor,
                  child: const Icon(
                    CupertinoIcons.play_rectangle_fill,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                Widgets.heightSpaceH2,
                Texts.textBlock(
                  "Add YouTube Video",
                  size: 22,
                ),
                Widgets.heightSpaceH1,
                Texts.textMedium(
                  "Please enter a valid YouTube video URL",
                  size: 14,
                ),
                Widgets.heightSpaceH2,
                EntryField(
                  controller: linkController,

                  hint: "Enter URL",
                ),
                Widgets.heightSpaceH3,
                Padding(
                  padding: PaddingConstants.screenPaddingHalf,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        child: CustomButton(
                          label: "Cancel",
                          borderColor: Colors.transparent,
                          backgroundColor: ColorConstants.silverColor,
                          textColor: ColorConstants.blackColor,
                          radius: 50,
                          onTap: () => Get.back(),
                        ),
                      ),
                      Widgets.widthSpaceW3,
                      Widgets.widthSpaceW1,
                      Expanded(
                        child: CustomButton(
                          label: "Confirm",
                          borderColor: Colors.transparent,
                          backgroundColor: ColorConstants.blackColor,
                          textColor: ColorConstants.whiteColor,
                          radius: 50,
                          onTap: () {
                            final link = linkController.text.trim();
                            if (isValidYoutubeUrl(link)) {
                              videoLinkController.text = link;
                              selectedVideo.value = null;
                              updateProfile();

                              update();
                            } else {
                              Widgets.showSnackBar(
                                'Invalid URL',
                                'Please enter a valid YouTube URL',
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  bool isValidYoutubeUrl(String url) {
    return url.isNotEmpty &&
        (url.contains('youtube.com/watch?v=') ||
            url.contains('youtu.be/') ||
            url.contains('youtube.com/embed/'));
  }

  Future<void> uploadPassportPhoto() async {
    if (passportImagePath == null) return;

    Widgets.showLoader("Uploading passport photo");
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateProfile}'),
      );

      var pic = await http.MultipartFile.fromPath(
        'passport_id_card',
        passportImagePath!,
      );
      request.files.add(pic);
      request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

      var response = await request.send();
      var responseData = await response.stream.bytesToString();
      var decodedData = jsonDecode(responseData);

      if (response.statusCode == 200 && decodedData['status'] == true) {
        fetchHostDetail();
        Widgets.showSnackBar("Success", "Passport photo updated successfully");
      } else {
        Widgets.showSnackBar("Error", "Failed to update passport photo");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  fetchReviews({int page = 1}) async {
    try {

      if (isReviewLoading.value) return;
      if (page == 1) {
        isReviewLoading.value = true;
      } else {
        isReviewMoreLoading.value = true;
      }
      var response = await ApiService.postData(Endpoints.reviews,{"page":page,"host_id":userController.userModel?.id}
      );
      isReviewLoading.value = false;
      isReviewMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          reviews.clear();
          totalReviews.value = 0;
          currentReviewPage.value = 1;
        }

        reviews.addAll(
          (response.data['ratings'] as List)
              .map((e) => rating.UserRatings.fromJson(e))
              .toList(),

        );

        totalReviews.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {print(e);
    isReviewLoading.value = false;
    isReviewMoreLoading.value = false;
    } finally {
      isReviewLoading.value = false;
      isReviewMoreLoading.value = false;
    }
  }

}
