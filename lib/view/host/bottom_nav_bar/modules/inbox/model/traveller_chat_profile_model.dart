class TravelChatProfile {
  int? id;
  String? firstName;
  String? lastName;
  String? email;
  String? fcmToken;
  String? image;
  String? country;
  String? countryCode;
  String? phone;
  String? instagramLink;
  String? longitude;
  String? latitude;
  int? isOnline;
  String? imageUrl;
  List<TripRequestsChat>? tripRequests;

  TravelChatProfile(
      {this.id,
        this.firstName,
        this.lastName,
        this.email,
        this.fcmToken,
        this.image,
        this.country,
        this.countryCode,
        this.phone,
        this.instagramLink,
        this.longitude,
        this.latitude,
        this.isOnline,
        this.imageUrl,
        this.tripRequests});

  TravelChatProfile.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    email = json['email'];
    fcmToken = json['fcm_token'];
    image = json['image'];
    country = json['country'];
    countryCode = json['country_code'];
    phone = json['phone'];
    instagramLink = json['instagram_link'];
    longitude = json['longitude'];
    latitude = json['latitude'];
    isOnline = json['is_online'];
    imageUrl = json['image_url'];
    if (json['trip_requests'] != null) {
      tripRequests = <TripRequestsChat>[];
      json['trip_requests'].forEach((v) {
        tripRequests!.add(new TripRequestsChat.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['email'] = this.email;
    data['fcm_token'] = this.fcmToken;
    data['image'] = this.image;
    data['country'] = this.country;
    data['country_code'] = this.countryCode;
    data['phone'] = this.phone;
    data['instagram_link'] = this.instagramLink;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['is_online'] = this.isOnline;
    data['image_url'] = this.imageUrl;
    if (this.tripRequests != null) {
      data['trip_requests'] =
          this.tripRequests!.map((v) => v?.toJson()).toList();
    }
    return data;
  }
}

class TripRequestsChat {
  int? id;
  int? userId;
  List<String>? country;
  String? startCity;
  String? endCity;
  String? startDate;
  String? endDate;
  String? description;
  String? budgetMin;
  String? budgetMax;
  String? interests;int? numberOfAttendees;
  int? status;
  String? createdAt;
  String? updatedAt;

  TripRequestsChat(
      {this.id,
        this.userId,
        this.country,
        this.startCity,
        this.endCity,
        this.startDate,this.numberOfAttendees,
        this.endDate,
        this.description,
        this.budgetMin,
        this.budgetMax,
        this.interests,
        this.status,
        this.createdAt,
        this.updatedAt});

  TripRequestsChat.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    country = json['country'].cast<String>();
    startCity = json['start_city'];
    endCity = json['end_city'];
    startDate = json['start_date'];
    endDate = json['end_date'];numberOfAttendees = json['no_of_attendees']??0;
    description = json['description'];
    budgetMin = json['budget_min'];
    budgetMax = json['budget_max'];
    interests = json['interests'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['country'] = this.country;
    data['start_city'] = this.startCity;
    data['end_city'] = this.endCity;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['description'] = this.description;
    data['budget_min'] = this.budgetMin;
    data['budget_max'] = this.budgetMax;
    data['interests'] = this.interests;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
