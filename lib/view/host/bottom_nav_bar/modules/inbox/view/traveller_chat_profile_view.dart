import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/constants/padding_constants.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../home/<USER>/home_controller.dart';

class TravellerChatProfileView extends StatefulWidget {
  const TravellerChatProfileView({super.key});

  @override
  State<TravellerChatProfileView> createState() =>
      _TravellerChatProfileViewState();
}

class _TravellerChatProfileViewState extends State<TravellerChatProfileView> {
  late HostHomeController homeController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    homeController = Get.find();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: InkWell(
          onTap: () => Get.back(),
          child: const Icon(Icons.arrow_back_ios_new_outlined, size: 20),
        ),
        title: Texts.textMedium("", fontWeight: FontWeight.w600, size: 20),
        centerTitle: true,
        backgroundColor: Colors.white,
      ),
      body: Obx(() {
        return homeController.isTravellerDetailLoading.value
            ? buildTravellerProfileShimmerEffect()
            : SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    AdvancedAvatar(
                      animated: true,
                      size: 120,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                          homeController.travellerDetail.value.imageUrl ?? "",
                          width: 200,
                          height: 200),
                    ),
                    Padding(
                      padding: PaddingConstants.screenPadding
                          .copyWith(top: 0, bottom: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Widgets.heightSpaceH1,
                          Texts.textBlock(
                              homeController.travellerDetail.value.firstName ?? "",
                              size: 18),
                          const SizedBox(
                            height: 2,
                          ),
                          Texts.textNormal(
                              homeController.travellerDetail.value.country ??
                                  "",
                              size: 11,
                              color: Colors.black54),
                        ],
                      ),
                    ),
                    Divider(
                      color: ColorConstants.greyColor,
                    ),
                    Padding(
                      padding: PaddingConstants.screenPadding.copyWith(top: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Texts.textBlock("Trips Requests", size: 16),
                          Widgets.heightSpaceH2,
                          if (homeController.travellerDetail.value.tripRequests
                                  ?.isEmpty ??
                              false)
                            Center(
                              child: Padding(
                                padding: const EdgeInsets.only(top: 108.0),
                                child: Texts.textNormal(
                                  "No trip requests yet",
                                  size: 12,
                                  color: ColorConstants.textColor,
                                ),
                              ),
                            )
                          else
                            ListView.separated(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: homeController.travellerDetail.value
                                      .tripRequests?.length ??
                                  0,
                              separatorBuilder: (context, index) =>
                                  Widgets.heightSpaceH3,
                              itemBuilder: (context, index) {
                                final request = homeController.travellerDetail.value
                                    .tripRequests?[index];
                                return Widgets.buildTravellerRequest(request: request);
                              },
                            ),
                          Widgets.heightSpaceH3,
                        ],
                      ),
                    )
                  ],
                ),
              );
      }),
    );
  }
}
