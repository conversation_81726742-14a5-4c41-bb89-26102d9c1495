import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:indyguide/core/constants/color_constants.dart';

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/widgets/video_player_screen.dart';
import '../../../../../../core/widgets/widgets.dart';

class MediaPreviewScreen extends StatelessWidget {
  final File file;
  final String fileType;
  final String thumbnail;
  final Function(String, File, String?) onSend;
  final TextEditingController messageController = TextEditingController();

  MediaPreviewScreen({
    Key? key,
    required this.file,
    required this.fileType,
    required this.thumbnail,
    required this.onSend,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,automaticallyImplyLeading: false,
        actions:[ IconButton(
          icon: const Icon(CupertinoIcons.clear_circled_solid, color: Colors.white  ),
          onPressed: () => Get.back(),
        ),],
        title: const Text(
          'Preview',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  if (fileType == 'image')
                    Image.file(
                      file,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    )
                  else if (fileType == 'video')
                    GestureDetector(
                      onTap: () {
                        Get.to(() => VideoPlayerScreen(
                          videoUrl: file.path,
                           isLocal: true,
                        ));
                      },
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          if (thumbnail.isNotEmpty)
                            Image.file(
                              File(thumbnail),
                              width: double.infinity,
                              fit: BoxFit.cover,
                            )
                          else
                            Container(
                              color: Colors.black45,
                              height: 300,
                              width: double.infinity,
                            ),
                          Icon(
                            Icons.play_circle_fill,
                            size: 50,
                            color: ColorConstants.whiteColor,
                          ),
                        ],
                      ),
                    )
                  else if (fileType == 'document')
                    Padding(padding: EdgeInsets.only(top:.3.sh),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white70,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            Icon(Icons.insert_drive_file, size: 50,color: Colors.white),
                            SizedBox(height: 8),
                            Text(
                              path.basename(file.path),
                              style: TextStyle(fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              color: ColorConstants.greyColor,
            ),
            padding: const EdgeInsets.only(right: 8.0, left: 8.0),
            margin: const EdgeInsets.all(8.0),
            child: Row(
              children: [

                Expanded(
                  child: TextField(
                    controller: messageController,
                    maxLines: null,

                    keyboardType: TextInputType.multiline,
                    style: const TextStyle(
                      color: Colors.black87,
                      fontFamily: "InstrumentSansRegular",
                    ),
                    decoration: InputDecoration(
                      hintText: "Message..",
                      fillColor: Colors.grey.shade200,
                      filled: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () async {

                    await onSend(
                      messageController.text.trim(),
                      file,
                      thumbnail.isEmpty ? null : thumbnail,
                    );

                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: ColorConstants.blackColor,
                      shape: BoxShape.circle,
                    ),
                    child: Image.asset(Assets.sendIcon, width: 20, height: 20),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}