import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:image_picker/image_picker.dart';

import 'package:indyguide/core/constants/color_constants.dart';

import 'package:indyguide/core/widgets/widgets.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/home_controller.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/inbox/controller/chat_controller.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/inbox/view/traveller_chat_profile_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/view/host_profile_detail_view.dart';
import 'package:intl/intl.dart';

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/services/socket_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/message_widgets.dart';
import '../../../../../../core/widgets/messagesendingtile.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../traveller/navigation/modules/inbox/model/chat_model.dart';
import '../../../../../traveller/navigation/modules/inbox/model/meessage_model.dart';

class HostChatView extends StatefulWidget {
  final OtherUser? chatUser; // Add this to receive user details
  final String? chatId; // Optional - for existing chats

  const HostChatView({
    Key? key,
    this.chatUser,
    this.chatId,
  }) : super(key: key);

  @override
  _HostChatViewState createState() => _HostChatViewState();
}

class _HostChatViewState extends State<HostChatView> {
  final TextEditingController message = TextEditingController();

  late HostChatController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find();
    SocketService.initializeSocket();
    initializeChat();

    // Add scroll listener
    controller.scrollMessageController.addListener(scrollListener);
  }

  Future<void> initializeChat() async {
    if (widget.chatId != null) {
      // Find existing chat from controller's chat list
      final existingChat = controller.chats
          .firstWhereOrNull((chat) => chat.chatId.toString() == widget.chatId);

      if (existingChat != null) {
        controller.selectedChat.value = existingChat;
        controller.markChatAsRead(existingChat.chatId!);
      } else {
        // If chat not found in local list, create new chat object
        controller.selectedChat.value = Chat(
          chatId: int.parse(widget.chatId!),
          otherUser: widget.chatUser,
          lastMessage: null,
        );
      }

      SocketService.subscribeToChatChannel(widget.chatId.toString());
    } else {
      controller.selectedChat.value = Chat(
        chatId: null,
        otherUser: widget.chatUser,
        lastMessage: null,
      );
    }

    await controller.fetchMessages(page: 1);
    Get.find<HostHomeController>()
        .fetchTravellerDetail(widget.chatUser!.id.toString());
  }

  @override
  void dispose() {
    controller.scrollMessageController.removeListener(scrollListener);

    super.dispose();
  }

  scrollListener() {
    if (controller.scrollMessageController.position.pixels ==
            controller.scrollMessageController.position.maxScrollExtent &&
        controller.totalMessages.value > controller.messages.length) {
      controller.fetchMessages(page: controller.currentMessagePage.value + 1);
      controller.currentMessagePage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Get.back();
          controller.changeWithinChat();
          return false;
        },
        child: Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            scrolledUnderElevation: 0,
            automaticallyImplyLeading: false,
            title: Column(
              children: [
                Row(children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                      controller.changeWithinChat();
                    },
                    child:
                        const Icon(Icons.arrow_back_ios_new_outlined, size: 20),
                  ),
                  Widgets.widthSpaceW2,
                  InkWell(
                    onTap: () {
                      Get.to(() => const TravellerChatProfileView());
                    },
                    child: Row(
                      children: [
                        AdvancedAvatar(
                          animated: true,
                          size: 35,
                          foregroundDecoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.grey,
                              width: 0.0,
                            ),
                          ),
                          child: widget.chatUser?.image == null
                              ? Text(
                                  widget.chatUser!.name!.substring(0, 1) ?? "")
                              : Widgets.networkImage(
                                  widget.chatUser?.image ?? "",
                                  width: 100,
                                  height: 100),
                        ),
                        Widgets.widthSpaceW2,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Texts.textBlock(widget.chatUser?.name?.split(" ")[0] ?? "_",
                                size: 15),
                            const SizedBox(
                              height: 2,
                            ),
                            Obx(
                              ()=> Text(
                                "${ Get.find<HostHomeController>().travellerDetail.value.tripRequests?.length ?? "0" } trip requests",
                                style: const TextStyle(
                                    color: Colors.black45,
                                    fontSize: 10,
                                    fontFamily: "InstrumentSansRegular"),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ]),
                Divider(color: ColorConstants.greyColor, thickness: .5),
              ],
            ),
          ),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(child: buildMessages()),
                buildMessageInput(),
              ],
            ),
          ),
        ));
  }

  Widget buildMessages() {
    return Obx(
      () {
        return Scrollbar(
          child: ListView(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            controller: controller.scrollMessageController,
            reverse: true,
            padding: const EdgeInsets.all(10.0),
            children: [
              if (controller.isMessagesLoadingMore.value) Widgets.moreLoading(),
              controller.isMessagesLoading.value
                  ? const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: ShimmerListSkeleton(),
                    )
                  : controller.messages.isNotEmpty
                      ? ListView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          reverse: true,
                          shrinkWrap: true,
                          itemCount: controller.messages.length,
                          itemBuilder: (context, index) {
                            Message message = controller.messages[index];
                            Message? previousMessage =
                                index < controller.messages.length - 1
                                    ? controller.messages[index + 1]
                                    : null;

                            Widget messageWidget =
                                controller.selectedChat.value.otherUser?.id !=
                                        message.senderId
                                    ? SentMessage(message: message)
                                    : ReceivedBubbleChat(
                                        message: message,
                                        onAvatarTap: () {},
                                        senderImage: controller.selectedChat
                                            .value.otherUser?.image,
                                      );

                            if (shouldShowTimeDivider(
                                message, previousMessage)) {
                              return Column(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    child: Center(
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: ColorConstants.greyColor
                                              .withOpacity(0.3),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          getMessageDateDivider(
                                              message.createdAt!),
                                          style: const TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12,
                                            fontFamily: "LatoRegular",
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  messageWidget,
                                ],
                              );
                            }

                            return messageWidget;
                          },
                        )
                      : Padding(
                          padding: EdgeInsets.only(bottom: .4.sh),
                          child:
                              Widgets.noRecordsFound(title: "No messages yet")),
            ],
          ),
        );
      },
    );
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              contentPadding: EdgeInsets.zero,
              dense: true,
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                controller.pickAndSendImage(source: ImageSource.camera);
              },
            ),
            Divider(color: ColorConstants.greyColor, thickness: .5),
            ListTile(
              contentPadding: EdgeInsets.zero,
              dense: true,
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                controller.pickAndSendImage(source: ImageSource.gallery);
              },
            ),
            Divider(color: ColorConstants.greyColor, thickness: .5),
            ListTile(
              contentPadding: EdgeInsets.zero,
              dense: true,
              leading: const Icon(Icons.videocam),
              title: const Text('Record Video'),
              onTap: () {
                Navigator.pop(context);
                controller.pickAndSendVideo(source: ImageSource.camera);
              },
            ),
            Divider(color: ColorConstants.greyColor, thickness: .5),
            ListTile(
              contentPadding: EdgeInsets.zero,
              dense: true,
              leading: const Icon(Icons.video_library),
              title: const Text('Choose Video'),
              onTap: () {
                Navigator.pop(context);
                controller.pickAndSendVideo(source: ImageSource.gallery);
              },
            ),
            Divider(color: ColorConstants.greyColor, thickness: .5),
            ListTile(
              contentPadding: EdgeInsets.zero,
              dense: true,
              leading: const Icon(Icons.attach_file),
              title: const Text('Send Document'),
              onTap: () {
                Navigator.pop(context);
                controller.pickAndSendFile();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget buildMessageInput() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        color: ColorConstants.greyColor,
      ),
      padding: const EdgeInsets.only(right: 8.0, left: 8.0),
      margin: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          InkWell(
            child: const Icon(
              Icons.attach_file,
              color: Colors.black54,
            ),
            onTap: _showAttachmentOptions,
          ),
          Expanded(
            child: TextField(
              controller: message,
              maxLines: null,
              keyboardType: TextInputType.multiline,
              style: const TextStyle(
                color: Colors.black87,
                fontFamily: "InstrumentSansRegular",
              ),
              decoration: InputDecoration(
                hintText: "Message..",
                fillColor: Colors.grey.shade200,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          InkWell(
            onTap: () => handleSendMessage(),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ColorConstants.blackColor,
                shape: BoxShape.circle,
              ),
              child: Image.asset(Assets.sendIcon, width: 20, height: 20),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> handleSendMessage() async {
    if (message.text.trim().isEmpty) return;

    final messageText = message.text;
    message.clear();

    // Create a temporary message
    final tempMessage = Message(
      id: Utils.generateUniqueNumber(),
      message: messageText,
      senderId: controller.userController.userModel?.id ?? 0,
      chatId: controller.selectedChat.value.chatId ?? 0,
      createdAt: DateTime.now().toString(),
      formattedCreatedAt: 'Just now',
      isLoading: true,
      // Add other required fields with default values
      fileType: 'text',
      fileName: null,
    );

    // Add temporary message to the list
    controller.messages.insert(0, tempMessage);
    controller.messages.refresh();

    // Scroll to bottom
    controller.scrollMessageController.animateTo(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );

    // Send the actual message
    await controller.sendMessage(text: messageText);
  }

  shouldShowTimeDivider(Message currentMessage, Message? previousMessage) {
    if (previousMessage == null) return true;

    DateTime currentTime = parseDateString(currentMessage.createdAt!);
    DateTime previousTime = parseDateString(previousMessage.createdAt!);

    return !Utils.isSameDay(currentTime, previousTime);
  }

  DateTime parseDateString(String dateStr) {
    try {
      // First try parsing with DateFormat
      return DateFormat("dd/MM/yyyy hh:mm a").parse(dateStr);
    } catch (e) {
      try {
        // If that fails, try standard DateTime.parse
        return DateTime.parse(dateStr);
      } catch (e) {
        // If all parsing fails, return current time
        print('Error parsing date: $dateStr');
        return DateTime.now();
      }
    }
  }

  String getMessageDateDivider(String dateTime) {
    DateTime messageDate = parseDateString(dateTime);
    DateTime now = DateTime.now();

    if (Utils.isSameDay(messageDate, now)) {
      return "Today";
    } else if (Utils.isSameDay(
        messageDate, now.subtract(const Duration(days: 1)))) {
      return "Yesterday";
    } else {
      return DateFormat("MMMM dd, yyyy").format(messageDate);
    }
  }
}
