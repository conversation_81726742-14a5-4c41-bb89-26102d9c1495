import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/view/traveller/navigation/modules/inbox/model/meessage_model.dart';
import 'dart:convert';
import '../../../../../../../../controller/user_controller.dart';
import '../../../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../../../core/services/http_service.dart';
import '../../../../../../../../core/services/socket_service.dart';
import '../../../../../../../../core/utils/utils.dart';
import '../../../../../../../../core/widgets/widgets.dart';
import '../../../../../../../traveller/navigation/modules/inbox/model/chat_model.dart';

import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:video_compress/video_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

import '../../../../home/<USER>/home_controller.dart';
import '../../../controller/chat_controller.dart';
import '../../media_preview_screen.dart';

class AdminChatController extends GetxController {
  final TextEditingController messageController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();
  final RxList<Message> messages = <Message>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool isMessagesLoading = false.obs;
  final RxBool isMessagesLoadingMore = false.obs;

  final RxInt currentMessagePage = 1.obs;
  final RxInt totalMessages = 0.obs;
  final ScrollController scrollMessageController = ScrollController();
  final ScrollController scrollChatsController = ScrollController();
  late UserController userController;
  @override
  void onInit() {
    userController = Get.find();
    super.onInit();
initializeSocketListeners();
  }

  void initializeSocketListeners() {
    try {
      print('Initializing socket listeners in AdminController');
      SocketService.initializeSocket().then((_) {
        print('Socket initialized, setting up listeners');
        SocketService.subscribeToAdminChannel(userController.userModel?.id.toString() ?? "");

        SocketService.listenToAdminMessages((data) {
          print('Message received in adminController: $data');
          if (data is Map<String, dynamic>) {
            handleNewMessage(data);
          }
        });

        SocketService.listenToAdminEdits((data) {
          print('Message edit received in HostChatController: $data');
          if (data is Map<String, dynamic>) {
            handleNewMessageEdited(data);
          }
        });

        SocketService.listenToAdminDeletes((data) {
          print('Message delete received in HostChatController: $data');
          if (data is Map<String, dynamic>) {
            handleNewMessageDeleted(data);
          }
        });
        SocketService.listenToUnReadNotificationCount((data) {
          print('Notification counts received in HostChatController: $data');
          if (data is Map<String, dynamic>) {
            handleNotificationCount(data);
          }
        });SocketService.listenToUnReadMessagesCount((data) {
          print('Message counts received in HostChatController: $data');
          if (data is Map<String, dynamic>) {
            handleMessagesCount(data);
          }
        });
      }).catchError((error) {
        print('Error during socket initialization: $error');
      });
    } catch (e) {
      print('Error initializing socket listeners: $e');
    }
  }



  @override
  void onClose() {
    // if (selectedChat.value.chatId != null) {
    //   SocketService.unsubscribeFromChatChannel(selectedChat.value.chatId.toString());
    // }SocketService.unsubscribeFromUserChannel(userController.userModel?.id.toString() ?? "");
    // SocketService.disconnect();

    SocketService.unsubscribeFromAdminChannel(userController.userModel?.id.toString() ?? "");
    VideoCompress.cancelCompression();
    super.onClose();
  }

  fetchMessages({int page = 1}) async {
    try {
      if (isMessagesLoading.value) return;
      if (page == 1) {
        isMessagesLoading.value = true;
      } else {
        isMessagesLoadingMore.value = true;
      }
      final response = await ApiService.getData(
          "${Endpoints.adminMessages}?page=$page");
      isMessagesLoadingMore.value = false;
      isMessagesLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          messages.clear();
          currentMessagePage.value = 1;
        }
        final List<Message> fetchedMessages =
        (response.data['messages'] as List)
            .map((message) => Message.fromJson(message))
            .toList();
        messages.addAll(fetchedMessages);
        totalMessages.value = response.data['pagination']['total'] ?? 0;
        currentMessagePage.value =
            response.data['pagination']['current_page'] ?? 1;
      }
    } catch (e) {
      print('Error fetching messages: $e');
    } finally {
      isMessagesLoadingMore.value = false;
      isMessagesLoading.value = false;
    }
  }

  void handleNewMessage(Map<String, dynamic> messageData) {
    try {
      final data = Message.fromJson(messageData);




            final tempMessageIndex = messages.indexWhere(
                    (msg) => msg.isLoading == true);

            if (tempMessageIndex != -1) {
              messages[tempMessageIndex] = data;
            } else {
              messages.insert(0, data);

              // Safe scroll handling
              if (scrollMessageController.hasClients &&
                  scrollMessageController.positions.isNotEmpty &&
                  scrollMessageController.position.pixels <=
                      scrollMessageController.position.minScrollExtent + 100) {
                try {
                  scrollMessageController.animateTo(
                    0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                } catch (e) {
                  print('Error scrolling: $e');
                }
              }
              messages.refresh();
          }




    } catch (e) {
      print('Error handling new message: $e');
    }
  }
  void handleNewMessageEdited(Map<String, dynamic> messageData) {
    try {
      final data = Message.fromJson(messageData);

      // Find and update the message in the messages list
      final messageIndex = messages.indexWhere((msg) => msg.id == data.id);
      if (messageIndex != -1) {
        messages[messageIndex] = data;
        messages.refresh();
      }

    } catch (e) {
      print('Error handling edited message: $e');
    }
  }
  changeWithinChat() async {
    try {

      var response = await ApiService.getData(
        Endpoints.changeAdminChatStatus,
      );

      if (response.status == true) {
      }
    } finally {
    }
  }
  void handleNewMessageDeleted(Map<String, dynamic> messageData) {
    try {
      if(messageData['message_id']==null) return;

      // Find and update the message in the messages list
      final messageIndex = messages.indexWhere((msg) => msg.id == messageData['message_id']);
      if (messageIndex != -1) {
        messages.removeAt(messageIndex);
        messages.refresh();
      }

    } catch (e) {
      print('Error handling edited message: $e');
    }
  }
  void handleMessagesCount(Map<String, dynamic> messageData) {
    try {
      Get.find<HostHomeController>().unreadMessagesCount.value=messageData['unread_count'];

    } catch (e) {
      print('Error handling message count: $e');
    }
  }
  void handleNotificationCount(Map<String, dynamic> messageData) {
    try {
      Get.find<HostHomeController>().unreadNotificationsCount.value=messageData['unread_notifications_count'];
    } catch (e) {
      print('Error handling notification count: $e');
    }
  }


  Future<void> sendMessage(
      {String? text, File? file, String? thumbnail}) async {
    try {
      if ((text == null || text.trim().isEmpty) && file == null) return;

      var request = http.MultipartRequest(
          'POST', Uri.parse('${Endpoints.baseURL}${Endpoints.adminSendMessages}'));
      if (file != null) {
        var fileField =
        await http.MultipartFile.fromPath('file', file.path ?? "");

        request.files.add(fileField);
      }
      if (thumbnail != null) {
        var thumbnailField = await http.MultipartFile.fromPath(
            'file_thumbnail', thumbnail ?? "");
        request.files.add(thumbnailField);
      }
      if (text != null && text.trim().isNotEmpty) {
        request.fields['message'] = text;
      }


      request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

      request.headers['Accept'] = 'application/json; charset=UTF-8';

      var response = await request.send();

      if (response.statusCode == 200) {
        var data = await response.stream.bytesToString();
        var decodedData = jsonDecode(data);

        if (decodedData['status'] == true) {}
      } else {
        throw Exception('Failed to send data to the server');
      }
    } catch (e) {
      print('Error sending message: $e');
    }
  }




  Future<void> pickAndSendImage({required ImageSource source}) async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
      );
      final directory = await getTemporaryDirectory();
      String newPath =
          '${directory.path}/image${Utils.generateUniqueNumber()}.jpg';

      // Compress the image using the flutter_image_compress package
      var result = await FlutterImageCompress.compressAndGetFile(
        pickedFile?.path ?? "",
        newPath,
        quality: 50, // Set the compression quality (0 to 100)
      );
      if (result != null) {
        Get.to(
              () => MediaPreviewScreen(
            file: File(result.path),
            fileType: 'image',
            thumbnail: "",
            onSend: (String message, File file, String? thumbnail) async {
              Widgets.showLoader("Sending message...");

              await sendMessage(
                text: message,
                file: file,
                thumbnail: thumbnail,
              );Widgets.hideLoader();
              Get.back();
            },
          ),
          transition: Transition.rightToLeft,
        );

      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  Future<void> pickAndSendVideo(
      {ImageSource source = ImageSource.gallery}) async {
    try {
      final XFile? pickedFile = await _imagePicker.pickVideo(
        source: source,
        maxDuration: const Duration(minutes: 2),
      );

      if (pickedFile != null) {
        // Check file size (100MB limit)
        final file = File(pickedFile.path);
        final sizeInBytes = await file.length();
        final sizeInMB = sizeInBytes / (1024 * 1024);

        if (sizeInMB > 100) {
          Widgets.showSnackBar(
            "Error",
            "Video size should be less than 100MB",
          );
          return;
        }



        try {


          // Generate thumbnail
          final thumbnail = await VideoCompress.getFileThumbnail(
            pickedFile.path,
            quality: 50,
            position: -1, // -1 means the middle of the video
          );



          final thumbnailFile = File(thumbnail.path);
          Get.to(
                () => MediaPreviewScreen(
              file: File(pickedFile.path),
              fileType: 'video',
              thumbnail: thumbnailFile.path,
              onSend: (String message, File file, String? thumbnail) async {
                Widgets.showLoader("Sending message...");

                await sendMessage(
                  text: message,
                  file: file,
                  thumbnail: thumbnail,
                );Widgets.hideLoader();
                Get.back();
              },
            ),
            transition: Transition.rightToLeft,
          );

        } catch (e) {
          Widgets.hideLoader();
          Widgets.showSnackBar("Error", "Failed to compress video: $e");
          return;
        }
      }
    } catch (e) {
      print('Error picking video: $e');
    }
  }

  Future<void> pickAndSendFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
      );

      if (result != null && result.files.single.path != null) {
        File file = File(result.files.single.path!);
        Get.to(
              () => MediaPreviewScreen(
            file: file,
            fileType: 'document',
            thumbnail: "",
            onSend: (String message, File file, String? thumbnail) async {
              Widgets.showLoader("Sending message...");

              await sendMessage(
                text: message,
                file: file,
                thumbnail: thumbnail,
              );Widgets.hideLoader();
              Get.back();

            },
          ),
          transition: Transition.rightToLeft,
        );

      }
    } catch (e) {
      print('Error picking file: $e');
    }
  }

  @override
  void dispose() {
    messageController.dispose();
    super.dispose();
  }
}
