import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/color_constants.dart';

import 'package:indyguide/core/widgets/widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/model/bookings_model.dart';

import '../../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../../core/widgets/text_widgets.dart';

import '../../../../../traveller/navigation/modules/inbox/model/chat_model.dart';
import '../../../controller/nav_controller.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/view/chat_view.dart';
import 'booking_details.dart';
import '../controller/booking_controller.dart';
import '../model/booking_model.dart';

class HostBookingsView extends StatefulWidget {
  HostBookingsView({super.key});

  @override
  State<HostBookingsView> createState() => _HostBookingsViewState();
}

class _HostBookingsViewState extends State<HostBookingsView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController scrollController = ScrollController();

  late HostBookingController controller;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _tabController.addListener(_handleTabChange);
    controller = Get.put(HostBookingController());
    controller.selectedTabIndex.value = 0;
    controller.selectedTabLabel.value = "All";
    controller.isBookingLoading.value = false;
    controller.isBookingMoreLoading.value = false;
    controller.fetchBookings(page: 1, status: controller.getStatusFromIndex(0));
    scrollController.addListener(scrollListener);
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      controller.userBookings.clear();
      controller.currentBookingPage.value = 1;
      controller.totalBooking.value = 0;
      controller.fetchBookings(
        page: 1,
        status: controller.getStatusFromIndex(_tabController.index),
      );
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalBooking.value > controller.userBookings.length) {
      controller.fetchBookings(
        page: controller.currentBookingPage.value + 1,
        status: controller.getStatusFromIndex(_tabController.index),
      );
      controller.currentBookingPage.value++;
    }
  }

  Widget buildTab(String label, int index) {
    return Obx(() {
      final isSelected = controller.selectedTabIndex.value == index;
      return GestureDetector(
        onTap: () {
          controller.selectedTabIndex.value = index;
          controller.selectedTabLabel.value = label;
          _tabController.animateTo(index);
        },
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 15,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? ColorConstants.blackColor
                : ColorConstants.silverColor,
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Center(
            child: Texts.textMedium(label,
                color: isSelected ? Colors.white : Colors.black, size: 12),
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        automaticallyImplyLeading: false,
        title: Texts.textMedium("My Bookings",
            size: 20, fontWeight: FontWeight.w600),
      ),
      body: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 15),
            height: 35,
            child: ListView(
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              children: [
                buildTab("All", 0),
                Widgets.widthSpaceW1,
                buildTab("Upcoming", 1),
                Widgets.widthSpaceW1,
                buildTab("In Progress", 2),
                Widgets.widthSpaceW1,
                buildTab("Completed", 3),
                Widgets.widthSpaceW1,
                buildTab("Cancel", 4),
              ],
            ),
          ),
          Widgets.heightSpaceH1,
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TabBarView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _tabController,
                  children: [
                    buildAllBookingsList(),
                    buildAllBookingsList(),
                    buildAllBookingsList(),
                    buildAllBookingsList(),
                    buildAllBookingsList(),
                  ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildAllBookingsList() {
    return Obx(
      () {
        return Scrollbar(
          child: ListView(
            controller: scrollController,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            children: [
              Widgets.heightSpaceH2,
              controller.isBookingLoading.value
                  ? const ShimmerListSkeleton()
                  : controller.userBookings.isNotEmpty
                      ? ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            HostBooking booking =
                                controller.userBookings[index];
                            return Widgets.buildHostBookingCard(
                                onDetailTap: () {
                                  controller.selectedBooking.value = booking;
                                  controller.fetchBookingDetail();
                                  Get.to(() => HostBookingDetailsView());
                                },
                                onChatTap: () {
                                  final existingChat =
                                      Get.find<HostChatController>()
                                          .chats
                                          .firstWhereOrNull((chat) =>
                                              chat.otherUser?.id ==
                                              booking.user?.id);
                                  existingChat == null
                                      ? Get.to(() => HostChatView(
                                            chatUser: OtherUser(
                                              id: booking.user?.id,
                                              name: "${booking.user?.firstName??""} ${booking.user?.lastName??""}",
                                              image: booking.user?.imageUrl,
                                            ),
                                          ))?.then((value) {
                                          // Get.find<HostNavController>()
                                          //     .changeIndex(1);
                                          // ;
                                        })
                                      : Get.to(() => HostChatView(
                                            chatId:
                                                existingChat?.chatId.toString(),
                                            chatUser: OtherUser(
                                              id: booking.user?.id,
                                              name: "${booking.user?.firstName??""} ${booking.user?.lastName??""}",

                                              image: booking.user?.imageUrl,
                                            ),
                                          ))?.then((value) {
                                          // Get.find<HostNavController>()
                                          //     .changeIndex(1);

                                        });
                                },
                                onCancelTap: () {
                                  controller.confirmationDialogue(
                                      context, booking.id.toString(), false);
                                },
                                booking: booking);
                          },
                          separatorBuilder: (context, index) {
                            return Widgets.heightSpaceH2;
                          },
                          itemCount: controller.userBookings.length ?? 0)
                      : Widgets.noRecordsFound(title: "No bookings so far"),
              if (controller.isBookingMoreLoading.value) Widgets.moreLoading(),
            ],
          ),
        );
      },
    );
  }
}
