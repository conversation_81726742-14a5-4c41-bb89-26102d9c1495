import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/view/leave_review_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/inbox/model/chat_model.dart';
import 'package:readmore/readmore.dart';

import '../../../../../../../core/constants/assets_constants.dart';
import '../../../../../../../core/constants/color_constants.dart';
import '../../../../../../../core/constants/padding_constants.dart';

import '../../../../../../../core/widgets/custom_button.dart';
import '../../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../../core/widgets/widgets.dart';
import '../../../controller/nav_controller.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/view/chat_view.dart';
import '../controller/booking_controller.dart';


class HostBookingDetailsView extends StatelessWidget {
  HostBookingController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "Booking Details"),
      body: SingleChildScrollView(
        padding: PaddingConstants.screenPaddingHalf,
        child: Obx(
              () => controller.isBookingDetailLoading.value
              ? buildBookingDetailEffect()
              : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textBlock("Trip Details", size: 14),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Widgets.buildTextGroup(
                        title: "Title",
                        value: controller.bookingDetail.value.title ?? "",
                        icon: Assets.titleIcon),
                  ),
                  Widgets.widthSpaceW4,
                  Expanded(
                    child: Widgets.buildStatusTextGroup(
                        title: "Status",
                        value: controller.bookingDetail.value.status
                            ?.toUpperCase() ??
                            "-",
                        icon: Assets.statusIcon),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Widgets.buildTextGroup(
                        title: "Start Date",
                        value: controller.bookingDetail.value.startDate ??
                            "-",
                        icon: Assets.calendarIcon),
                  ),
                  Widgets.widthSpaceW4,
                  Expanded(
                    child: Widgets.buildTextGroup(
                        title: "End Date",
                        value:
                        controller.bookingDetail.value.endDate ?? "-",
                        icon: Assets.calendarIcon),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              const Text(
                "Additional Notes" ?? "",
                style: TextStyle(
                    fontSize: 10,
                    color: Colors.black54,
                    fontFamily: "InstrumentSansRegular"),
              ),
              const SizedBox(height: 5),
              ReadMoreText(
                controller.bookingDetail.value.additionalNotes ?? "-",
                trimLines: 8,

                trimMode: TrimMode.Line,
                trimCollapsedText: 'Show more',
                trimExpandedText: 'Show less',
                lessStyle: TextStyle(
                    fontSize: 13,
                    color: Colors.black),
                moreStyle: TextStyle(
                    fontSize: 13,
                    color:  Colors.black),
                style: TextStyle(
                    fontSize: 12, color: Colors.black,)),


              Widgets.heightSpaceH1,
              Divider(
                thickness: .5,
                color: ColorConstants.greyColor,
              ),
              Widgets.heightSpaceH1,
              Texts.textBlock("Traveller Details", size: 14),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Widgets.buildTextGroup(
                        title: "Traveller Name",
                        value: "${controller.bookingDetail.value.user?.firstName??""} ",

                        icon: Assets.profileIcon),
                  ),
                  Widgets.widthSpaceW4,
                  Expanded(
                    child: Widgets.buildTextGroup(
                        title: "Deposit Paid",
                        value:
                        "${controller.bookingDetail.value.depositAmount ?? "0"}€ ",
                        icon: Assets.dollarIcon),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Widgets.buildTextGroup(
                        title: "Email",
                        value:
                        controller.bookingDetail.value.user?.email ??
                            '',
                        icon: Assets.smsIcons),
                  ),
                  Widgets.widthSpaceW4,
                  Expanded(
                    child: Widgets.buildTextGroup(
                        title: "Phone",
                        value:
                        controller.bookingDetail.value.user?.phone ??
                            '----',
                        icon: Assets.phoneIcon),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              Widgets.buildTextGroup(
                  title: "Location",
                  value: controller.bookingDetail.value.location ?? "-",
                  icon: Assets.locationIcon),

              Widgets.heightSpaceH1,


              if (controller.bookingDetail.value.rating != null) ...[
                Divider(
                  thickness: .5,
                  color: ColorConstants.greyColor,
                ),
                Widgets.heightSpaceH1,
                Texts.textBlock("Traveller Review", size: 14),
                Widgets.heightSpaceH1,
                Widgets.buildRatingStar(
                  controller.bookingDetail.value.rating?.rating?.toDouble() ?? 0,
                ),
                if (controller.bookingDetail.value.rating?.review != null) ...[
                  Widgets.heightSpaceH1,
                  Texts.textMedium(
                    controller.bookingDetail.value.rating?.review ?? "",
                    size: 12,
                    color: ColorConstants.textColor,
                  ),
                ],
                if ((controller.bookingDetail.value.rating?.images?.isNotEmpty ?? false)) ...[
                  Widgets.heightSpaceH2,
                  SizedBox(
                    height: 100,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemCount: controller.bookingDetail.value.rating?.images?.length ?? 0,
                      separatorBuilder: (context, index) => SizedBox(width: 8),
                      itemBuilder: (context, index) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Widgets.networkImage(
                            controller.bookingDetail.value.rating?.images?[index].imageUrl ?? "",
                            height: 100,
                            width: 100,

                          ),
                        );
                      },
                    ),
                  ),
                ],
                Widgets.heightSpaceH3,
              ],
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: CustomButton(
                      onTap: () {
                        final existingChat =
                        Get.find<HostChatController>()
                            .chats
                            .firstWhereOrNull((chat) =>
                        chat.otherUser?.id ==
                            controller.bookingDetail.value.user?.id);
                        existingChat == null
                            ? Get.to(() => HostChatView(
                          chatUser: OtherUser(
                            id: controller.bookingDetail.value.user?.id,
                            name: "${controller.bookingDetail.value.user?.firstName} ${controller.bookingDetail.value.user?.lastName??""}",
                            image: controller.bookingDetail.value.user?.imageUrl,
                          ),
                        ))?.then((value) {
                          // Get.find<HostNavController>()
                          //     .changeIndex(1);Get.back();

                        })
                            : Get.to(() => HostChatView(
                          chatId:
                          existingChat.chatId.toString(),
                          chatUser: OtherUser(
                              id: controller.bookingDetail.value.user?.id,
                              name: "${controller.bookingDetail.value.user?.firstName} ${controller.bookingDetail.value.user?.lastName??""}",

                              image: controller.bookingDetail.value.user?.imageUrl
                          ),
                        ))?.then((value) {
                          // Get.find<HostNavController>()
                          //     .changeIndex(1);Get.back();

                        });
                      },
                      label: "Chat with Traveller",
                      textColor: ColorConstants.blackColor,
                      fontSize: 12,
                      backgroundColor: ColorConstants.primaryColor,
                      radius: 50,
                    ),
                  ),
                  Widgets.widthSpaceW3,

                  if (controller.bookingDetail.value.status?.toLowerCase() == "pending")
                    Expanded(
                      child: CustomButton(
                        onTap: () {
                          controller.confirmationDialogue(
                            context,
                            controller.bookingDetail.value.id.toString(),
                            true,
                          );
                        },
                        label: "Cancel Booking",
                        textColor: ColorConstants.whiteColor,
                        radius: 50,
                        fontSize: 12,
                        backgroundColor: ColorConstants.redColor,
                      ),
                    ),
                ],
              ),
              Widgets.heightSpaceH1,
            ],
          ),
        ),
      ),
    );
  }
}
