class HostBooking {
  int? id;
  String? title;
  String? startDate;
  String? endDate;
  String? depositAmount;
  String? status;
  int? guideUserId;
  int? userId;
  String? location;
  User? user;

  HostBooking(
      {this.id,
        this.title,
        this.startDate,
        this.endDate,
        this.depositAmount,
        this.status,
        this.guideUserId,
        this.userId,
        this.location,
        this.user});

  HostBooking.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    depositAmount = json['deposit_amount'].toString();
    status = json['status'];
    guideUserId = json['guide_user_id'];
    userId = json['user_id'];
    location = json['location'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['deposit_amount'] = this.depositAmount;
    data['status'] = this.status;
    data['guide_user_id'] = this.guideUserId;
    data['user_id'] = this.userId;
    data['location'] = this.location;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}

class User {
  int? id;
  String? firstName  ;String? lastName;
  String? image;
  String? imageUrl;

  User({this.id, this.image, this.imageUrl,this.firstName,this.lastName});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
 firstName = json['first_name'];lastName = json['last_name'];
    image = json['image'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['image'] = this.image;
    data['image_url'] = this.imageUrl;
    return data;
  }
}
