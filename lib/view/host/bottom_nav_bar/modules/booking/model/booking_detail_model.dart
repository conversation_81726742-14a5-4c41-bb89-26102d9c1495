import '../../../../../traveller/navigation/modules/booking/model/booking_detail_model.dart';

class HostBookingDetail {
  int? id;
  int? userId;
  int? guideUserId;
  String? title;
  String? startDate;
  String? endDate;
  String? location;
  String? additionalNotes;
  String? depositAmount;
  String? status;
  String? paymentStatus;
  String? stripeSessionId;
  String? stripePaymentIntent;
  String? totalAmount;
  String? paidAt;
  String? createdAt;
  String? updatedAt;
  User? user;
  Rating? rating;
  HostBookingDetail(
      {this.id,
        this.userId,
        this.guideUserId,
        this.title,
        this.startDate,
        this.endDate,
        this.location,
        this.additionalNotes,
        this.depositAmount,
        this.status,
        this.paymentStatus,
        this.stripeSessionId,
        this.stripePaymentIntent,
        this.totalAmount,this.rating,
        this.paidAt,
        this.createdAt,
        this.updatedAt,
        this.user});

  HostBookingDetail.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    guideUserId = json['guide_user_id'];
    title = json['title'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    location = json['location'];
    additionalNotes = json['additional_notes'];
    depositAmount = json['deposit_amount'].toString();
    status = json['status'];
    paymentStatus = json['payment_status'];
    stripeSessionId = json['stripe_session_id'];
    stripePaymentIntent = json['stripe_payment_intent'];
    totalAmount = json['total_amount'].toString();
    paidAt = json['paid_at'];
    createdAt = json['created_at'];
    rating = json['rating'] != null ? Rating.fromJson(json['rating']) : null;

    updatedAt = json['updated_at'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['guide_user_id'] = this.guideUserId;
    data['title'] = this.title;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['location'] = this.location;
    data['additional_notes'] = this.additionalNotes;
    data['deposit_amount'] = this.depositAmount;
    data['status'] = this.status;
    data['payment_status'] = this.paymentStatus;
    data['stripe_session_id'] = this.stripeSessionId;
    data['stripe_payment_intent'] = this.stripePaymentIntent;
    data['total_amount'] = this.totalAmount;
    data['paid_at'] = this.paidAt;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}

class User {
  int? id;

  String? email;
  String? image;
  String? phone;
  String? imageUrl;
  String? firstName  ;String? lastName;
  User({this.id, this.email, this.image, this.phone, this.imageUrl,this.firstName,this.lastName});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];lastName = json['last_name'];

    email = json['email'];
    image = json['image'];
    phone = json['phone'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;

    data['email'] = this.email;
    data['image'] = this.image;
    data['phone'] = this.phone;
    data['image_url'] = this.imageUrl;
    return data;
  }
}
