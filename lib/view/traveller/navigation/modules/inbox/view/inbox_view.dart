import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../host/bottom_nav_bar/modules/inbox/view/admin_chat/view/admin_chat_view.dart';
import '../controller/chat_controller.dart';
import '../model/chat_model.dart';
import 'chat_view.dart';


class TravellerInboxView extends StatefulWidget {
  TravellerInboxView({super.key});

  @override
  State<TravellerInboxView> createState() => _TravellerInboxViewState();
}

class _TravellerInboxViewState extends State<TravellerInboxView> {
late ChatController controller;
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    controller= Get.find();controller.fetchChatsBackground();
    scrollController.addListener(scrollListener);
  }

  @override
  void dispose() {
    scrollController.removeListener(scrollListener);
    super.dispose();
  }


scrollListener() {
  if (scrollController.position.pixels ==
      scrollController.position.maxScrollExtent &&
      controller.totalChats.value >
          controller.chats.length) {
    controller.fetchChats(
        page:  controller.currentPage.value + 1);
    controller.currentPage.value++; // Increment the page counter
  }
}
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            Texts.textMedium(
              "Inbox",
              size: 20,
              fontWeight: FontWeight.w600,
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(15.0),
        child: buildChats(),
      ),
    );
  }
Widget buildChats() {
  return Obx(
        () {
      return RefreshIndicator(
        onRefresh: () async {
          await controller.fetchChats(page: 1);
        },
        child: ListView(shrinkWrap: true,
          controller: scrollController,
          children: [
            InkWell(
                onTap: () {
                  Get.to(() => AdminChatView(

                  ));
                },
                child: Widgets.adminChatCard()),Widgets.heightSpaceH2,
            controller.isLoading.value
                ? const ShimmerListSkeleton()
                :controller.chats.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final chat = controller.chats[index];
                  return InkWell(

                    onTap: (){

                      controller.selectedChat.value=chat;

                      Get.to(()=> ChatView(chatId: chat.chatId.toString(),chatUser: chat.otherUser,));
                    },

                      child: Widgets.chatCard(chat: chat));

                },
                separatorBuilder: (context, index) {
                  return Divider(color: ColorConstants.greyColor, thickness: .5);
                },
                itemCount:controller.chats.length ?? 0)
                : Widgets.noRecordsFound(title: "No Chats"),
            if (  controller.isLoadingMore.value)
              Widgets.moreLoading(),
          ],
        ),
      );
    },
  );
}
}
