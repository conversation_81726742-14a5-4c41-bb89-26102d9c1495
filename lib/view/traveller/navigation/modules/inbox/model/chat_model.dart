class Chat {
  int? chatId;
  OtherUser? otherUser;
  LastMessage? lastMessage;
  int? unreadCount;
  Chat({this.chatId, this.otherUser, this.lastMessage});

  Chat.fromJson(Map<String, dynamic> json) {
    chatId = json['chat_id'];
    unreadCount= json['unread_count']??0;
    otherUser = json['other_user'] != null
        ? new OtherUser.fromJson(json['other_user'])
        : null;
    lastMessage = json['last_message'] != null
        ? new LastMessage.fromJson(json['last_message'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chat_id'] = this.chatId;
    if (this.otherUser != null) {
      data['other_user'] = this.otherUser!.toJson();
    }
    if (this.lastMessage != null) {
      data['last_message'] = this.lastMessage!.toJson();
    }
    return data;
  }
}

class OtherUser {
  int? id;
  String? name;
  String? image;

  OtherUser({this.id, this.name, this.image});

  OtherUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['image'] = this.image;
    return data;
  }
}

class LastMessage {
  int? id;
  String? message; String? messageType; String? fileName;
  int? senderId;
  int? isRead;
  String? createdAt;

  LastMessage(
      {this.id, this.message, this.senderId, this.isRead, this.createdAt,this.fileName,this.messageType});

  LastMessage.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    message = json['message'];  messageType = json['file_type'];fileName = json['file_name'];
    senderId = json['sender_id'];
    isRead = json['is_read']??0;
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['message'] = this.message;
    data['sender_id'] = this.senderId;
    data['is_read'] = this.isRead;
    data['created_at'] = this.createdAt;
    return data;
  }
}
