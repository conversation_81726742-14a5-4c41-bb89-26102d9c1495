class Message {
  int? id;
  int? chatId;
  int? senderId;
  String? message;
  String? file;
  String? fileType;
  String? fileName;
  String? fileUrl;
  int? isRead;  int? isAdmin;
  String? createdAt;
  String? updatedAt;String? thumbnail;
  String? formattedCreatedAt;
  bool? isLoading;
  Message({
    this.id,
    this.chatId,
    this.senderId,this.thumbnail,
    this.message,
    this.file,this.isLoading,
    this.fileType,
    this.fileName,
    this.fileUrl,this.isAdmin,
    this.isRead,
    this.createdAt,
    this.updatedAt,
    this.formattedCreatedAt,
  });

  Message.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chatId = json['chat_id'];
    senderId = json['sender_id'];
    message = json['message'];
    file = json['file'];  isLoading=false;
    fileType = json['file_type'];   thumbnail = json['file_thumbnail_url'];
    fileName = json['file_name'];
    fileUrl = json['file_url'];
    isRead = json['is_read'];  isAdmin = json['is_admin_message'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    formattedCreatedAt = json['formatted_created_at'];
  }
}
