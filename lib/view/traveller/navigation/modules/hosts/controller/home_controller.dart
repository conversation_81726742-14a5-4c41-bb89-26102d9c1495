import 'dart:developer';
import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/view/payment_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/model/traveller_host.dart';
import 'package:intl/intl.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/services/http_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../../model/notifications_model.dart';
import '../model/host_detail_model.dart'as host;
import '../model/host_detail_model.dart';

class TravellerHomeController extends GetxController {
  RxString userLocation = "".obs;

  RxDouble latitude = 0.0.obs;
  RxDouble longitude = 0.0.obs;
  RxBool isLoading = false.obs;
  late UserController userController;
  RxInt totalHosts = 0.obs; // Rx variable to check if there's more data
  RxInt currentHostPage = 0.obs;
  RxBool isHostMoreLoading = false.obs;
  RxBool isHoDetailLoading = false.obs;
  var hostDetail = host.HostDetail().obs;
  TextEditingController descriptionController = TextEditingController();
  TextEditingController titleController = TextEditingController();
  TextEditingController depositAmountController = TextEditingController();
  final RxInt unreadNotificationsCount = 0.obs;
  final RxInt unreadMessagesCount = 0.obs;
  RxString startDate = "".obs;
  RxString endDate = "".obs;
  var selectedHost = TravellerHost().obs;

  RxList recommendedHosts = <TravellerHost>[].obs;
  RxBool isRecommendedLoading = false.obs;
  RxBool isRecommendedMoreLoading = false.obs;
  RxInt totalRecommendedHosts = 0.obs; // Rx variable to check if there's more data
  RxInt currentRecommendedHostPage = 0.obs;
  RxList notifications = <Notifications>[].obs;
  RxBool isNotificationLoading = false.obs;
  RxBool isNotificationMoreLoading = false.obs;
  RxInt totalNotifications = 0.obs;
  RxInt currentNotificationPage = 0.obs;


  RxList reviews = <UserRatings>[].obs;
  RxBool isReviewLoading = false.obs;
  RxBool isReviewMoreLoading = false.obs;
  RxInt totalReviews = 0.obs;
  RxInt currentReviewPage = 0.obs;
  @override
  void onInit() {
    super.onInit();
    userController = Get.find();userController.updateFcmToken();
   fetchRecommendedHosts();
    getCurrentLocation();fetchUnreadNotificationsCount();
  }
  Future<void> fetchUnreadNotificationsCount() async {
    try {
      var response = await ApiService.getData(Endpoints.notificationCount);
      if (response.status == true) {
        unreadNotificationsCount.value = response.data['unread_notifications_count'] ?? 0;
        unreadMessagesCount.value = response.data['unread_messages_count'] ?? 0;

      }
    } catch (e) {
      print('Error fetching unread notifications count: $e');
    }
  }

  Future<void> getCurrentLocation() async {
    isLoading.value = true;
    try {
      if (!await checkLocationServices()) return;
      if (!await requestLocationPermission()) return;

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      latitude.value = position.latitude;
      longitude.value = position.longitude;

    } catch (e) {
      print("Error fetching location: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> checkLocationServices() async {
    if (!await Geolocator.isLocationServiceEnabled()) {
      print('Location services are disabled.');
      return false;
    }
    return true;
  }

  fetchHostDetail() async {
    try {
      isHoDetailLoading.value = true;
      print(selectedHost.value.id);
      var response = await ApiService.getData(
        "${Endpoints.hostProfile}/${selectedHost.value.id}",
      );
      isHoDetailLoading.value = false;

      if (response.status == true) {
        hostDetail.value = host.HostDetail.fromJson(response.data['host']);
      }
    } finally {
      isHoDetailLoading.value = false;
    }
  }

  Future<bool> requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        print('Location permissions are denied');
        return false;
      }
    }
    if (permission == LocationPermission.deniedForever) {
      print('Location permissions are permanently denied.');
      return false;
    }
    return true;
  }

  fetchRecommendedHosts({int page = 1}) async {
    try {
      if (isRecommendedLoading.value) return;
      if (page == 1) {
        isRecommendedLoading.value = true;
      } else {
        isRecommendedMoreLoading.value = true;
      }
      var response = await ApiService.getData(Endpoints.recommendedHosts
      );
      isRecommendedLoading.value = false;
      isRecommendedMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          recommendedHosts.clear();
          totalRecommendedHosts.value = 0;
          currentRecommendedHostPage.value = 1;
        }

        recommendedHosts.addAll(
          (response.data['hosts'] as List)
              .map((e) => TravellerHost.fromJson(e))
              .toList(),
        );

        totalRecommendedHosts.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isRecommendedLoading.value = false;
      isRecommendedMoreLoading.value = false;
    } finally {
      isRecommendedLoading.value = false;
      isRecommendedMoreLoading.value = false;
    }
  }
  backgroundRecommendedHosts({int page = 1}) async {
    try {

      if (page == 1) {
      } else {
        isRecommendedMoreLoading.value = true;
      }
      var response = await ApiService.getData(Endpoints.recommendedHosts
      );

      isRecommendedMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          recommendedHosts.clear();
          totalRecommendedHosts.value = 0;
          currentRecommendedHostPage.value = 1;
        }

        recommendedHosts.addAll(
          (response.data['hosts'] as List)
              .map((e) => TravellerHost.fromJson(e))
              .toList(),
        );

        totalRecommendedHosts.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isRecommendedMoreLoading.value = false;
    } finally {
      isRecommendedMoreLoading.value = false;
    }
  }


  Future<void> selectDate(BuildContext context, bool isStartDate) async {
    DateTime firstDate = isStartDate
        ? DateTime.now()
        : (startDate.value.isNotEmpty
            ? DateFormat('dd/MM/yyyy').parse(startDate.value)
            : DateTime.now());
    DateTime lastDate = isStartDate
        ? (endDate.value.isNotEmpty
            ? DateFormat('dd/MM/yyyy').parse(endDate.value)
            : DateTime(2100))
        : DateTime(2100);

    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: firstDate,
      firstDate: firstDate,
      lastDate: lastDate, builder: (context, child) {
      return Theme(
        data: Theme.of(context).copyWith(
          colorScheme: ColorScheme.light(
            primary: ColorConstants.primaryColor,
          ),
        ),
        child: child!,
      );
    },
    );

    if (picked != null) {
      String formattedDate = Utils.formatDate(picked);

      if (isStartDate) {
        startDate.value = formattedDate;
        if (endDate.value.isNotEmpty &&
            DateFormat('dd/MM/yyyy').parse(endDate.value).isBefore(picked)) {
          endDate.value =
              formattedDate; // Adjust endDate if it's before startDate
        }
      } else {
        if (startDate.value.isNotEmpty &&
            picked.isBefore(DateFormat('dd/MM/yyyy').parse(startDate.value))) {
          Widgets.showSnackBar(
            "Invalid Date",
            "End date cannot be earlier than start date.",
          );
        } else {
          endDate.value = formattedDate;
        }
      }
    }
  }

  requestForDeposit() async {
    try {
      if (titleController.text.isEmpty) {
        return Widgets.showSnackBar("Alert", "Title is required");
      }
     if (startDate.value=="") {
        return Widgets.showSnackBar("Alert", "Start Date is required");
      }
       if (endDate.value=="") {
        return Widgets.showSnackBar("Alert", "End Date is required");
      }  if (depositAmountController.text.isEmpty) {
        return Widgets.showSnackBar("Alert", "Deposit amount is required");
      }
      Widgets.showLoader("Loading...");
      var data = {
        "guide_user_id":hostDetail.value.id.toString(),
        'title': titleController.text,
        'start_date': startDate.value,
        "end_date":endDate.value,
        'additional_notes': descriptionController.text,
        "deposit_amount":depositAmountController.text

      };
      var response = await ApiService.postData(Endpoints.createDeposit, data);
      Widgets.hideLoader();

      if (response.status == true) {

       Get.to(()=>PaymentProcessView(url: response.data['payment_url']??"",));
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }
  fetchNotifications({int page = 1}) async {
    try {   unreadNotificationsCount.value=0;
      if (isNotificationLoading.value) return;
      if (page == 1) {
        isNotificationLoading.value = true;
      } else {
        isNotificationMoreLoading.value = true;
      }
      var response = await ApiService.getData("${Endpoints.notifications}?page=$page",
      );
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          notifications.clear();
          totalNotifications.value = 0;
          currentNotificationPage.value = 1;
        }

        notifications.addAll(
          (response.data['notifications'] as List)
              .map((e) => Notifications.fromJson(e))
              .toList(),

        );

        totalNotifications.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {print(e);
    isNotificationLoading.value = false;
    isNotificationMoreLoading.value = false;
    } finally {
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
    }
  }
  fetchReviews({int page = 1}) async {
    try {

      if (isReviewLoading.value) return;
      if (page == 1) {
        isReviewLoading.value = true;
      } else {
        isReviewMoreLoading.value = true;
      }
      var response = await ApiService.postData(Endpoints.reviews,{"page":page,"host_id":hostDetail.value.id}
      );
      isReviewLoading.value = false;
      isReviewMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          reviews.clear();
          totalReviews.value = 0;
          currentReviewPage.value = 1;
        }

        reviews.addAll(
          (response.data['ratings'] as List)
              .map((e) => UserRatings.fromJson(e))
              .toList(),

        );

        totalReviews.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {print(e);
    isReviewLoading.value = false;
    isReviewMoreLoading.value = false;
    } finally {
      isReviewLoading.value = false;
      isReviewMoreLoading.value = false;
    }
  }

}
