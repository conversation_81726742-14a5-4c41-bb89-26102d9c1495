import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:indyguide/core/constants/assets_constants.dart';

import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/services/http_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../model/Host_detail_model.dart';
import '../model/traveller_host.dart';
import '../view/host_profile_detail_view.dart';
import 'home_controller.dart';

class SearchHostsController extends GetxController {
  RxBool isMapView = false.obs; // Default: List View
  RxInt totalHosts = 0.obs; // Rx variable to check if there's more data
  RxInt currentHostPage = 0.obs;
  RxBool isHostMoreLoading = false.obs;
  RxBool isSearchActive = false.obs;
  RxBool isHoDetailLoading = false.obs;
  var hostDetail = HostDetail().obs;
  var selectedHost = TravellerHost().obs;
  RxList hosts = <TravellerHost>[].obs;
  RxBool isHostLoading = false.obs;
  void toggleView() {
    isMapView.value = !isMapView.value;
  }
  TextEditingController searchFieldController = TextEditingController();

  Timer? debounce;
  var selectedLocation = "".obs;  var selectedLocationLat = "".obs;  var selectedLocationLong = "".obs;
  var selectedCity = "".obs;  var selectedCountry = "".obs;
  var selectedSorts = <String>[].obs;
  var selectedRatings = <String>[].obs;
  var selectedLanguages = <String>[].obs;
  var selectedGender = <String>[].obs;
  final Rx<Marker?> selectedMarker = Rx<Marker?>(null);

  final Rx<GoogleMapController?> _mapController =
      Rx<GoogleMapController?>(null);
  final Rx<Set<Marker>> markers = Rx<Set<Marker>>({});

  @override
  void onInit() {
    super.onInit();
    _loadMarkers();
  }

  RxDouble minBudget = 0.0.obs;
  RxDouble maxBudget = 1000.0.obs;

  void updateBudget(double start, double end) {
    minBudget.value = start;
    maxBudget.value = end;
  }



  void toggleSorts(String interest) {
    if (selectedSorts.contains(interest)) {
      selectedSorts.remove(interest);
    } else {
      selectedSorts.add(interest);
    }
  }

  void toggleRatings(String interest) {
    String numericRating = interest.replaceAll(RegExp(r'[^0-9]'), '');print(numericRating);
    selectedRatings.clear();
    selectedRatings.add(numericRating);
  }

  void toggleGender(String interest) {
    if (selectedGender.contains(interest)) {
      selectedGender.remove(interest);
    } else {
      selectedGender.add(interest);
    }
  }

  void toggleLanguages(String interest) {
    if (selectedLanguages.contains(interest)) {
      selectedLanguages.remove(interest);
    } else {
      selectedLanguages.add(interest);
    }
  }

  void onMapCreated(GoogleMapController controller) {
    _mapController.value = controller;
  }

  Future<void> _loadMarkers() async {


    Set<Marker> tempMarkers = {};
    final Uint8List markerIcon =
        await Utils.getBytesFromAsset(Assets.pinIcon, 25);
    for (var data in hosts) {
      tempMarkers.add(
        Marker(
          markerId: MarkerId(data["id"]),
          position: LatLng(data["lat"], data["lng"]),
          infoWindow: InfoWindow(title: data["name"]),
          icon: BitmapDescriptor.bytes(markerIcon),
          onTap: () {
            selectedMarker.value = Marker(
              markerId: MarkerId(data["id"]),
              position: LatLng(data["lat"], data["lng"]),
              infoWindow: InfoWindow(title: data["name"]),
            );
          },
        ),
      );
    }

    markers.value = tempMarkers;
  }

  fetchHosts(String? query,{int page = 1}) async {
    try {isSearchActive.value=true;
      if (isHostLoading.value) return;
      if (page == 1) {
        isHostLoading.value = true;
      } else {
        isHostMoreLoading.value = true;
      }
      var response = await ApiService.postData(Endpoints.hostsFilter, {
        "city": selectedCity.value,"latitude":selectedLocationLat.value,"longitude":selectedLocationLong.value,
        "query":query??"","page":page,"country":selectedCountry.value,
        "sort_by": selectedSorts.join(","),"ratings":selectedRatings.join(","),
        "gender":selectedGender.join(",")
        ,"languages":selectedLanguages.join(",")
      });

      isHostLoading.value = false;
      isHostMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          hosts.clear();
          totalHosts.value = 0;
          currentHostPage.value = 1;        markers.value = {};


        }

        hosts.addAll(
          (response.data['hosts'] as List)
              .map((e) => TravellerHost.fromJson(e))
              .toList(),
        );

        totalHosts.value = response.data['pagination']['total'] ?? 0;
        _updateMapMarkers();
      }
    } catch (e) {
      isHostLoading.value = false;
      isHostMoreLoading.value = false;
    } finally {
      isHostLoading.value = false;
      isHostMoreLoading.value = false;
    }
  }

  Future<void> _updateMapMarkers() async {
    try {
      Set<Marker> tempMarkers = {};
      final Uint8List markerIcon = await Utils.getBytesFromAsset(Assets.pinIcon, 25);

      for (var host in hosts) {
        // Only add marker if host has valid coordinates
        if (host.latitude != null && host.longitude != null) {
          final LatLng position = LatLng(
            double.parse(host.latitude!),
            double.parse(host.longitude!)
          );

          tempMarkers.add(
            Marker(
              markerId: MarkerId(host.id.toString()),
              position: position,

              icon: BitmapDescriptor.bytes(markerIcon),
              onTap: () {
                selectedMarker.value = Marker(
                  markerId: MarkerId(host.id.toString()),
                  position: position,
                  infoWindow: InfoWindow(
                    title: "${host.firstName} ${host.lastName}",
                    snippet: host.country ?? "",
                  ),
                );
                _mapController.value?.animateCamera(
                  CameraUpdate.newCameraPosition(
                    CameraPosition(
                      target: position,
                      zoom: 13.0,
                    ),
                  ),
                );
                onMarkerTap(host);
              },
            ),
          );
        }
      }

      // Update markers on map
      markers.value = tempMarkers;

      // If markers exist, move camera to fit all markers
      if (tempMarkers.isNotEmpty) {

        _mapController.value!.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(
               tempMarkers.first.position.latitude,
             tempMarkers.first.position.longitude,
              ),
              zoom: 5.0,
            ),
          ),
        );
      }
    } catch (e) {
      print('Error updating markers: $e');
    }
  }

  LatLngBounds _getBounds(Set<Marker> markers) {
    double? minLat, maxLat, minLng, maxLng;

    for (Marker marker in markers) {
      if (minLat == null || marker.position.latitude < minLat) {
        minLat = marker.position.latitude;
      }
      if (maxLat == null || marker.position.latitude > maxLat) {
        maxLat = marker.position.latitude;
      }
      if (minLng == null || marker.position.longitude < minLng) {
        minLng = marker.position.longitude;
      }
      if (maxLng == null || marker.position.longitude > maxLng) {
        maxLng = marker.position.longitude;
      }
    }

    return LatLngBounds(
      southwest: LatLng(minLat!, minLng!),
      northeast: LatLng(maxLat!, maxLng!),
    );
  }

  void clearFilters() {
    selectedLocation.value = "";selectedLocationLat.value="";selectedLocationLong.value="";
    selectedGender.clear();
    selectedRatings.clear();
    selectedSorts.clear();
    selectedLanguages.clear();selectedCountry.value="";selectedCity.value="";
  }
  onSearchTextChanged(String query) {
    if (debounce?.isActive ?? false) debounce!.cancel();

    debounce = Timer(const Duration(milliseconds: 500), () {
      fetchHosts(query,page: 1);
    });
  }

  void onMarkerTap(TravellerHost host) {
    selectedHost.value = host;
    Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 4,
              width: 40,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            GestureDetector(
              onTap: () {
                Get.back();
                Get.find<TravellerHomeController>().selectedHost.value = host;
                Get.find<TravellerHomeController>()
                    .selectedHost
                    .value = host;  Get.find<TravellerHomeController>().fetchHostDetail();
                Get.to(() =>  HostProfileDetailView(fromChat: false,));

              },
              child: Widgets.hostCard(host: host),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
    );
  }
}
