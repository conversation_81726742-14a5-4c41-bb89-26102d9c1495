import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/core/widgets/custom_button.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/core/widgets/widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/controller/home_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/controller/search_controller.dart';

import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';

import '../model/traveller_host.dart';
import 'filter_view.dart';
import 'host_profile_detail_view.dart';

class SearchHostsView extends StatefulWidget {
  const SearchHostsView({super.key});

  @override
  State<SearchHostsView> createState() => _SearchHostsViewState();
}

class _SearchHostsViewState extends State<SearchHostsView> {
  late SearchHostsController homeController;

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    homeController = Get.put(SearchHostsController());
   homeController.isSearchActive.value=false;
    homeController.isHostMoreLoading.value = false;
    homeController.isHostLoading.value = false;
    homeController.clearFilters();homeController.fetchHosts("",
        page:1);
    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        homeController.totalHosts.value > homeController.hosts.length) {
      homeController.fetchHosts(homeController.searchFieldController.text,
          page: homeController.currentHostPage.value + 1);
      homeController.currentHostPage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        titleSpacing: 0,scrolledUnderElevation: 0,
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: const Icon(
                    Icons.arrow_back_ios_new_outlined,
                    size: 20,
                  )),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: EntrySearchField(
                  onChange: (v) {
                    homeController.onSearchTextChanged(v!);
                  },
                  prefixIcon: Assets.searchIcon,
                  hint: "Search",
                  suffixIcon: Assets.filterIcon,
                  onTrailingTap: () {
                    Get.to(() => const FilterView());
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [ Widgets.heightSpaceH1,
          Padding(
            padding: PaddingConstants.screenPaddingLess,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() => Texts.textBold(
                  homeController.isSearchActive.value?'Showing ${homeController.totalHosts.value} Results':"",
                  size: 14
                )),
                buildToggleSwitch()
              ],
            ),
          ),
          Widgets.heightSpaceH2,
          Obx(() => homeController.isMapView.value
              ? Expanded(
                  child: Stack(children: [
                  GoogleMap(
                    initialCameraPosition: CameraPosition(
                      target: LatLng(48.0196, 66.9237), // Kazakhstan Center
                      zoom: 6.0,
                    ),
                    onMapCreated: homeController.onMapCreated,
                    markers: homeController.markers.value,
                  ),

                ]))
              : buildHostsList()),
        ],
      ),
    );
  }

  buildToggleSwitch() {
    return Obx(() => GestureDetector(
          onTap: homeController.toggleView,
          child: AnimatedContainer(
            duration: Duration(milliseconds: 300),
            decoration: BoxDecoration(
              color: ColorConstants.greyColor, // Background color
              borderRadius: BorderRadius.circular(20), // Rounded pill shape
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: homeController.isMapView.value
                        ? ColorConstants.selectedNavIcon
                        : ColorConstants.greyColor, // Background color
                    borderRadius:
                        BorderRadius.circular(20), // Rounded pill shape
                  ),
                  child: Icon(
                    Icons.map,
                    color: homeController.isMapView.value
                        ? Colors.white
                        : Colors.black,
                    size: 18,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: homeController.isMapView.value
                        ? ColorConstants.greyColor
                        : ColorConstants.selectedNavIcon, // Background color
                    borderRadius:
                        BorderRadius.circular(20), // Rounded pill shape
                  ),
                  child: Icon(
                    Icons.list,
                    color: homeController.isMapView.value
                        ? Colors.black
                        : Colors.white,
                    size: 18,
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget buildHostsList() {
    return Obx(
      () {
        return Expanded(
          child: ListView(
            shrinkWrap: true,
            padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 10),
            controller: scrollController,
            children: [
              homeController.isHostLoading.value
                  ? const ShimmerListSkeleton()
                  : homeController.hosts.isNotEmpty
                      ? ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            TravellerHost host = homeController.hosts[index];
                            return GestureDetector(
                                onTap: () {
                                  Get.find<TravellerHomeController>()
                                      .selectedHost
                                      .value = host;  Get.find<TravellerHomeController>().fetchHostDetail();
                                  Get.to(() =>  HostProfileDetailView(fromChat: false,));

                                },
                                child: Widgets.hostCard(host: host));
                          },
                          separatorBuilder: (context, index) {
                            return Widgets.heightSpaceH1;
                          },
                          itemCount:  homeController.hosts.length ?? 0)
                      : Widgets.noRecordsFound(title: homeController.isSearchActive.value==false? "filter hosts here":"No hosts"),
              if (homeController.isHostMoreLoading.value) Widgets.moreLoading(),
            ],
          ),
        );
      },
    );
  }
}
