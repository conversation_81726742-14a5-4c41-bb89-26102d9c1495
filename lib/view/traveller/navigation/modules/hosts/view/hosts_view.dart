import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:indyguide/controller/user_controller.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';

import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/core/widgets/widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/controller/home_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/model/traveller_host.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/view/filter_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/view/host_profile_detail_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/view/notification_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/view/search_hosts_view.dart';

import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../settings/view/notification_screen.dart';
import '../../trip_requests/view/create_request_view.dart';
import '../controller/search_controller.dart';

class TravellerHomeView extends StatefulWidget {
  const TravellerHomeView({super.key});

  @override
  State<TravellerHomeView> createState() => _TravellerHomeViewState();
}

class _TravellerHomeViewState extends State<TravellerHomeView> {

  late TravellerHomeController homeController;
  final ScrollController scrollController = ScrollController();
  late SearchHostsController searchController;

  @override
  void initState() {
    super.initState();
    homeController = Get.find();
    searchController = Get.put(SearchHostsController());

    homeController.isRecommendedLoading.value = false;
    homeController.isRecommendedMoreLoading.value = false;
    homeController.backgroundRecommendedHosts(page: 1);
    scrollController.addListener(scrollListener);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (homeController.userController.isNewAccount==true) {
        Get.to(() => const CreateTripRequestView());
        homeController.userController.isNewAccount = false;
        homeController.userController.saveStatus();
        homeController.userController.update();
      }
    });
  }
  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        homeController.totalRecommendedHosts.value >
            homeController.recommendedHosts.length) {
      homeController.backgroundRecommendedHosts(
          page: homeController.currentRecommendedHostPage.value + 1);
      homeController.currentRecommendedHostPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(

      backgroundColor: Colors.white,
      appBar: AppBar(automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        title: GetBuilder<UserController>(init: UserController(),
          builder: (controller) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    AdvancedAvatar(
                      animated: true,
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey,
                          width: 0.0,
                        ),
                      ),

                      child:  controller.userModel?.imageUrl == null
                          ? Text(controller.userModel!.firstName!.substring(0, 1)):Widgets.networkImage(
                          controller.userModel?.imageUrl ?? "",
                          width: 100,
                          height: 100),
                    ),

                    const SizedBox(
                      width: 10,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textNormal('Hello, Welcome Back!',
                            color: ColorConstants.textColor, size: 9),
                        Texts.textBold(                                   "${controller.userModel?.firstName??""} ${controller.userModel?.lastName??''}",
                            size: 16),
                      ],
                    ),
                  ],
                ),

                Stack(clipBehavior: Clip.none,
                  children: [
                    GestureDetector(
                      onTap: () {  homeController.fetchNotifications(page: 1);
                      Get.to(() => const TravellerNotificationsView());
                      },
                      child: Image.asset(
                        Assets.notificationIcon,
                        height: 22,
                        width: 22,
                      ),
                    ),
                    Positioned(
                      right: -2,
                      top: -2,
                      child: Obx(() {
                        final count = homeController.unreadNotificationsCount.value;
                        return count > 0
                            ? CircleAvatar(
                          radius: 7,
                          backgroundColor: Colors.red,
                          child: Text(
                            count > 9 ? '9+' : count.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 7,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                            : const SizedBox.shrink();
                      }),
                    ),
                  ],
                ),

              ],
            );
          }
        ),
      ),
      body: buildHostsList(),

    );
  }

  Widget buildHostsList() {
    return Obx(
          () {
        return ListView(shrinkWrap: true,  padding: PaddingConstants.screenPaddingLess,
          controller: scrollController,
          children: [
            EntrySearchField(readOnly: true,
              prefixIcon: Assets.searchIcon,
              hint: "Search",
              suffixIcon: Assets.filterIcon,
              onTrailingTap: () {      Get.to(()=>const SearchHostsView());          Get.to(()=>FilterView());
              },onTap: (){Get.to(()=>const SearchHostsView());},
            ),
            Widgets.heightSpaceH1,
            Texts.textBold('Popular Hosts', size: 14),
            Widgets.heightSpaceH2,
            homeController.isRecommendedLoading.value
                ? const ShimmerListSkeleton()
                : homeController.recommendedHosts.isNotEmpty
                ? ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  TravellerHost host =
                  homeController.recommendedHosts[index];
                  return GestureDetector(

                      onTap: (){
                       homeController.selectedHost.value=host;homeController.fetchHostDetail();
                       Get.to(() =>  HostProfileDetailView(fromChat: false,));

                      },

                      child:Widgets.hostCard(host: host));

                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH1;
                },
                itemCount:homeController.recommendedHosts.length ?? 0)
                : Widgets.noRecordsFound(title: "No hosts"),
            if (homeController.isRecommendedMoreLoading.value)
              Widgets.moreLoading(),
          ],
        );
      },
    );
  }

}
