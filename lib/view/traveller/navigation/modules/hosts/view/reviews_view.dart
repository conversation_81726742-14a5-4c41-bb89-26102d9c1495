import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/view/host/bottom_nav_bar/modules/home/<USER>/home_controller.dart';

import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/user_review.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../traveller/navigation/modules/settings/controller/profile_controller.dart';
import '../controller/home_controller.dart';
import '../model/host_detail_model.dart';

class TravellerReviewView extends StatefulWidget {
  const TravellerReviewView({super.key});

  @override
  State<TravellerReviewView > createState() => _TravellerReviewViewState();
}

class _TravellerReviewViewState extends State<TravellerReviewView> {
  late TravellerHomeController homeController;
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    homeController = Get.find();


    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        homeController.totalReviews.value >
            homeController.reviews.length) {
      homeController.fetchReviews(
          page: homeController.currentReviewPage.value + 1);
      homeController
          .currentReviewPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "All Reviews"),
      body: buildReviews(),
    );
  }

  Widget buildReviews() {
    return Obx(
          () {
        return Padding(
          padding: const EdgeInsets.all(15.0),
          child: Scrollbar(
            child: ListView(
              shrinkWrap: true,
              physics: BouncingScrollPhysics(),
              controller: scrollController,
              children: [
                Column(crossAxisAlignment: CrossAxisAlignment.center,
                    children: [

                      Texts.textMedium("Overall Rating",
                          color: ColorConstants.primaryColor, size: 14),
                      Widgets.heightSpaceH1,
                      Texts.textBlock(
                          homeController.hostDetail.value.hostProfile?.ratings ?? "0",
                          size: 45),
                      Widgets.heightSpaceH05,
                      Center(
                          child: Widgets.buildRatingStar(
                              double.parse(homeController
                                  .hostDetail.value.hostProfile!.ratings
                                  .toString()),
                              isCenter: true,
                              size: 28)),
                      Widgets.heightSpaceH05,
                      Texts.textMedium(
                          "Based on ${homeController.hostDetail.value.hostProfile?.reviewsCount ?? 0} review(s)",
                          size: 13),

                    ]),
                Widgets.heightSpaceH3,
                Widgets.heightSpaceH3,
                homeController.isReviewLoading.value
                    ? Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: const ShimmerListSkeleton(),
                )
                    : homeController.reviews.isNotEmpty
                    ? ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      UserRatings? rating =
                      homeController.reviews?[index];
                      return ReviewSection(
                        name: "${rating?.user?.firstName??""}" ?? "",
                        image: rating?.user?.imageUrl ?? "",
                        dateAndTime: rating?.date ?? "",
                        comment: rating?.review??"",
                        rating: rating?.rating?.toDouble() ?? 0,
                        images: rating?.images,
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Divider(thickness: 0.8,color: ColorConstants.greyColor,);
                    },
                    itemCount: homeController.reviews.length ?? 0)
                    : Widgets.noRecordsFound(title: "No reviews"),
                if (homeController.isReviewMoreLoading.value)
                  Widgets.moreLoading(),
              ],
            ),
          ),
        );
      },
    );
  }
}
