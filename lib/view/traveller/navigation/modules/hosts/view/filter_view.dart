import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';

import 'package:indyguide/view/traveller/navigation/modules/hosts/view/select_location_view.dart';

import '../../../../../../core/constants/constants_list.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/search_controller.dart';

class FilterView extends StatefulWidget {
  const FilterView({super.key});

  @override
  State<FilterView> createState() => _FilterViewState();
}

class _FilterViewState extends State<FilterView> {
  final SearchHostsController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: CustomButton(
                label: "Clear Filters",
                fontSize: 12,
                borderColor: ColorConstants.blackColor,
                textColor: ColorConstants.blackColor,
                radius: 50,
                onTap: () {controller.clearFilters();},
              ),
            ),
            Widgets.widthSpaceW3,
            Expanded(
              child: CustomButton(
                label: "Search",
                fontSize: 12,
                borderColor: Colors.transparent,
                backgroundColor: ColorConstants.primaryColor,
                textColor: ColorConstants.blackColor,
                radius: 50,
                onTap: () {

                  Get.back();

                  controller.fetchHosts(controller.searchFieldController.text,page: 1);},


              ),
            ),
          ],
        ),
      ),
      appBar: Widgets.customAppBar(title: "Filter By"),
      body: SingleChildScrollView(
        padding: PaddingConstants.screenPaddingLess,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () {
                Get.to(() => const SelectLocationView())?.then((result) {
                  if (result != null) {
                    controller.selectedLocationLat.value = result['lat'] ?? "";
                    controller.selectedLocationLong.value = result['lng'] ?? "";
                    controller.selectedCountry.value = result['country'] ?? "";
                    controller.selectedCity.value = result['city'] ?? "";

                    controller.selectedLocation.value =
                        controller.selectedCity.value +
                            ", " +
                            controller.selectedCountry.value;
                  }
                });
              },
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
                const Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: Colors.black54,
                ),
                const SizedBox(
                  width: 5,
                ),
                Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock(
                          fontWeight: FontWeight.w600,
                          "Location",
                          size: 10,
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        Obx(
                          () => Text(
                              controller.selectedLocation.value == ""
                                  ? "Select"
                                  : controller.selectedLocation.value,
                              style: TextStyle(
                                fontFamily: "InstrumentSansRegular",
                                fontSize: 11,
                                color: ColorConstants.splash,
                                fontWeight: FontWeight.w800,
                              )),
                        ),
                      ]),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.black54,
                ),
              ]),
            ),
            const SizedBox(
              height: 7,
            ),
            Divider(
              color: ColorConstants.greyColor,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  Icons.language,
                  size: 16,
                  color: Colors.black54,
                ),
                Texts.textBlock(
                  fontWeight: FontWeight.w600,
                  "  Language",
                  size: 10,
                ),
              ],
            ),
            const SizedBox(
              height: 7,
            ),
            Obx(() => Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 8,
                  children: Data.languages.map((interest) {
                    bool isSelected =
                        controller.selectedLanguages.contains(interest);

                    return ChoiceChip(
                      label: Text(interest,
                          style: TextStyle(
                              fontFamily: "InstrumentSansRegular",
                              fontSize: 10,
                              color: isSelected
                                  ? ColorConstants.splash
                                  : Colors.black54,
                              fontWeight: FontWeight.w800)),
                      selected: controller.selectedLanguages.contains(interest),
                      onSelected: (selected) {
                        controller.toggleLanguages(interest);
                      },
                      showCheckmark: false,
                      selectedColor: ColorConstants.lightOrange,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(20), // Rounded edges
                        side: isSelected
                            ? BorderSide(
                                color: ColorConstants.splash,
                                width: 1) // Highlight selected
                            : const BorderSide(color: Colors.transparent),
                      ),
                      backgroundColor: ColorConstants.greyColor,
                    );
                  }).toList(),
                )),
            Widgets.heightSpaceH1,
            Divider(
              color: ColorConstants.greyColor,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  Icons.star_border_outlined,
                  size: 16,
                  color: Colors.black54,
                ),
                Texts.textBlock(
                  fontWeight: FontWeight.w600,
                  "  Ratings",
                  size: 10,
                ),
              ],
            ),
            const SizedBox(
              height: 7,
            ),
            Obx(() => Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 8,
                  children: Data.ratings.map((interest) {
                    bool isSelected = controller.selectedRatings
                        .contains(interest.replaceAll(RegExp(r'[^0-9]'), ''));

                    return ChoiceChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.star,
                            size: 10,
                            color: ColorConstants.splash,
                          ),
                          Text(
                            " $interest",
                            style: TextStyle(
                              fontFamily: "InstrumentSansRegular",
                              fontSize: 10,
                              color: isSelected
                                  ? ColorConstants.splash
                                  : Colors.black54,
                              fontWeight: FontWeight.w800,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        // Clear previous selection and select new one
                        controller.selectedRatings.clear();
                        controller.toggleRatings(interest);
                      },
                      showCheckmark: false,
                      selectedColor: ColorConstants.lightOrange,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                        side: isSelected
                            ? BorderSide(color: ColorConstants.splash, width: 1)
                            : const BorderSide(color: Colors.transparent),
                      ),
                      backgroundColor: ColorConstants.greyColor,
                    );
                  }).toList(),
                )),
            Widgets.heightSpaceH1,
            Divider(
              color: ColorConstants.greyColor,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  Icons.male_outlined,
                  size: 16,
                  color: Colors.black54,
                ),
                Texts.textBlock(
                  fontWeight: FontWeight.w600,
                  "  Gender",
                  size: 10,
                ),
              ],
            ),
            const SizedBox(
              height: 7,
            ),
            Obx(() => Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 8,
                  children: Data.gender.map((interest) {
                    bool isSelected =
                        controller.selectedGender.contains(interest);

                    return ChoiceChip(
                      label: Text(interest,
                          style: TextStyle(
                              fontFamily: "InstrumentSansRegular",
                              fontSize: 10,
                              color: isSelected
                                  ? ColorConstants.splash
                                  : Colors.black54,
                              fontWeight: FontWeight.w800)),
                      selected: controller.selectedGender.contains(interest),
                      onSelected: (selected) {
                        controller.toggleGender(interest);
                      },
                      showCheckmark: false,
                      selectedColor: ColorConstants.lightOrange,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(20), // Rounded edges
                        side: isSelected
                            ? BorderSide(
                                color: ColorConstants.splash,
                                width: 1) // Highlight selected
                            : const BorderSide(color: Colors.transparent),
                      ),
                      backgroundColor: ColorConstants.greyColor,
                    );
                  }).toList(),
                )),
            Widgets.heightSpaceH1,
            Divider(
              color: ColorConstants.greyColor,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  Icons.sort,
                  size: 16,
                  color: Colors.black54,
                ),
                Texts.textBlock(
                  fontWeight: FontWeight.w600,
                  "  Sort By",
                  size: 10,
                ),
              ],
            ),
            const SizedBox(
              height: 7,
            ),
            Obx(() => Wrap(
                  spacing: 8,
                  children: Data.sortS.map((interest) {
                    bool isSelected =
                        controller.selectedSorts.contains(interest);

                    return ChoiceChip(
                      label: Text(interest,
                          style: TextStyle(
                              fontFamily: "InstrumentSansRegular",
                              fontSize: 10,
                              color: isSelected
                                  ? ColorConstants.splash
                                  : Colors.black54,
                              fontWeight: FontWeight.w800)),
                      selected: controller.selectedSorts.contains(interest),
                      onSelected: (selected) {
                        controller.toggleSorts(interest);
                      },
                      showCheckmark: false,
                      selectedColor: ColorConstants.lightOrange,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(20), // Rounded edges
                        side: isSelected
                            ? BorderSide(
                                color: ColorConstants.splash,
                                width: 1) // Highlight selected
                            : const BorderSide(color: Colors.transparent),
                      ),
                      backgroundColor: ColorConstants.greyColor,
                    );
                  }).toList(),
                )),
            
            Widgets.heightSpaceH5,
            Widgets.heightSpaceH5,
          ],
        ),
      ),
    );
  }

  Widget buildBudgetInput(RxDouble value) {
    return Expanded(
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstants.greyColor),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Obx(() => Text(
                    "${value.value.toInt()}",
                    style: const TextStyle(fontSize: 13, color: Colors.black45),
                  )),
            ),
            Texts.textNormal("€", color: Colors.black54, size: 14),
          ],
        ),
      ),
    );
  }
}
