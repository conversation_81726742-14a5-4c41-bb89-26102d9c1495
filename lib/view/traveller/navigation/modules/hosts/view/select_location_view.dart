import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/custom_dropdown.dart';
import 'package:indyguide/core/widgets/entry_field.dart';

import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';

class SelectLocationView extends StatefulWidget {
  const SelectLocationView({super.key});

  @override
  State<SelectLocationView> createState() => _SelectLocationViewState();
}

class _SelectLocationViewState extends State<SelectLocationView> {
  TextEditingController searchField = TextEditingController();
  Timer? _debounce;
  final FocusNode searchFieldFocusNode = FocusNode();
  @override
  void dispose() {
    searchFieldFocusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  List<dynamic> places = [];
  Future<void> getCurrentLocation() async {
    Widgets.showLoader("fetching current location");
    try {
      bool serviceEnabled;
      LocationPermission permission;

      // Check if location services are enabled
      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        // Location services are not enabled
        return Future.error('Location services are disabled.');
      }

      // Check for location permission
      permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Permissions are denied
          return Future.error('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Permissions are permanently denied
        return Future.error('Location permissions are permanently denied.');
      }

      // Get the current location
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Reverse geocode the location to get the city and country
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );
      Widgets.hideLoader();
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        String city = place.locality ?? 'Unknown';
        String country = place.country ?? 'Unknown';
         var data=  {
          "lat":position.latitude.toString(),
          "lng":position.longitude.toString(),
           "city":"$city","country":country
        };
        Get.back(result: data);
      }
    } catch (e) {
      print("Error fetching location: $e");
    } finally {
      Widgets.hideLoader();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // bottomNavigationBar: Padding(
      //   padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
      //   child: CustomButton(
      //     label: "Search",
      //     fontSize: 12,
      //     borderColor: Colors.transparent,
      //     backgroundColor: ColorConstants.primaryColor,
      //     textColor: ColorConstants.blackColor,
      //     radius: 50,
      //     onTap: () {},
      //   ),
      // ),
      appBar: Widgets.customAppBar(title: "Select Location"),
      body: SingleChildScrollView(
        padding: PaddingConstants.screenPaddingLess,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            EntrySearchField(
              prefixIcon: Assets.searchIcon,
              hint: "Search City",
              controller: searchField,
              onChange: (value) {
                if (_debounce?.isActive ?? false) {
                  _debounce?.cancel();
                }
                if (value!.isNotEmpty) {
                  _debounce = Timer(const Duration(milliseconds: 500), () {
                    search(value);
                  });
                } else {
                  setState(() {
                    places = [];
                  });
                }
              },
            ),
            Widgets.heightSpaceH1,
            InkWell(
              onTap: () {
                getCurrentLocation();
              },
              child: Row(children: [
                Icon(Icons.near_me),
                SizedBox(width: 10),
                Expanded(
                  child: Texts.textMedium("Current Location", size: 14),
                ),
              ]),
            ),
            Widgets.heightSpaceH1,

            Divider(
              color: ColorConstants.greyColor,
            ),
            // Widgets.heightSpaceH1,
            // Texts.textBlock(
            //   fontWeight: FontWeight.w600,
            //   "All Locations",
            //   size: 14,
            // ), Widgets.heightSpaceH1,
            // const Divider(thickness: .5,),
            if (places.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: places
                    .map((place) => InkWell(
                  onTap: () async {
                    try {
                      Widgets.showLoader("Fetching location details");
                      final String placeId = place['place_id'];
                      final String description = place['description'];

                      // Get place details using place_id
                      Dio dioService = Dio();
                      final response = await dioService.get(
                        "${Endpoints.fetchPlaceFromGoogle}?place_id=$placeId&key=${Endpoints.googleKey}",
                      );

                      if (response.statusCode == 200 && response.data['result'] != null) {
                        final location = response.data['result']['geometry']['location'];
                        final double lat = location['lat'];
                        final double lng = location['lng'];
print(location.toString());
                        Get.back(result: {
                          "lat": lat.toString(),
                          "lng": lng.toString(),
                          "city":place['description'].split(",")[0]??"","country":place['description'].split(",")[1]??""
                        });
                      } else {
                        Widgets.showSnackBar("Error", "Could not fetch location coordinates");
                      }
                    } catch (e) {
                      print("Error getting coordinates: $e");
                      Widgets.showSnackBar("Error", "Failed to get location details");
                    } finally {
                      Widgets.hideLoader();
                    }
                  },
                          child: Column(
                            children: [
                              Widgets.heightSpaceH1,
                              Row(children: [
                                Icon(
                                  Icons.location_on_outlined,
                                  color: ColorConstants.primaryColor,
                                  size: 18,
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Texts.textMedium(place['description'],
                                      size: 14, color: Colors.black87),
                                ),
                              ]),
                              Widgets.heightSpaceH1,
                              const Divider(
                                thickness: .5,
                              ),
                            ],
                          ),
                        ))
                    .toList(),
              )
            else
              Widgets.noRecordsFound(title: "No locations"),
            // Widgets.heightSpaceH2,
            // ListView.separated(
            //     shrinkWrap: true,
            //     itemBuilder: (context, index) {
            //       return Column(
            //           crossAxisAlignment: CrossAxisAlignment.start,
            //           children: [
            //             Texts.textBlock(
            //               fontWeight: FontWeight.w600,
            //               "Spain",
            //               size: 10,
            //             ),
            //             SizedBox(
            //               height: 3,
            //             ),
            //             Text("14 tours",
            //                 style: TextStyle(
            //                   fontFamily: "InstrumentSansRegular",
            //                   fontSize: 11,
            //                   color: Colors.black54,
            //                 )),
            //           ]);
            //     },
            //     separatorBuilder: (context, index) {
            //       return Widgets.heightSpaceH2;
            //     },
            //     itemCount: 7)
          ],
        ),
      ),
    );
  }

  void search(String input) async {
    if (input.isEmpty) {
      setState(() {
        places = [];
      });
      return;
    }
    try {
      Dio dioService = Dio();
      final response = await dioService.get(
        "${Endpoints.fetchPlacesFromGoogle}?input=$input&types=(cities)&key=${Endpoints.googleKey}", // Use types parameter to limit to cities
      );
      print(response.toString());
      if (response.statusCode == 200) {
        setState(() {
          places = response.data['predictions'];
        });
      }
    } catch (e) {
      print('Error fetching places: $e');
      setState(() {
        places = [];
      });
    }
  }
}
