import 'dart:ffi';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/view/pay_deposit_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/hosts/view/reviews_view.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:readmore/readmore.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/constants/constants_list.dart';
import '../../../../../../core/widgets/custom_button.dart';

import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/user_review.dart';
import '../../../../../../core/widgets/video_player_screen.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../host/bottom_nav_bar/modules/settings/view/profile_view.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/model/chat_model.dart';
import '../../inbox/view/chat_view.dart';
import '../../settings/view/profile_webview.dart';
import '../controller/home_controller.dart';
import '../model/Host_detail_model.dart';
import '../model/host_detail_model.dart' as host;

class HostProfileDetailView extends StatefulWidget {
  HostProfileDetailView({this.fromChat});
 bool? fromChat;
  @override
  State<HostProfileDetailView> createState() => _HostProfileDetailViewState();


}

class _HostProfileDetailViewState extends State<HostProfileDetailView> {

  late TravellerHomeController homeController;

  @override
  void initState() {
    super.initState();
    homeController = Get.find();

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () {
          return homeController.isHoDetailLoading.value?buildShimmerEffect():  homeController.hostDetail.value.hostProfile!=null?SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  alignment: Alignment.bottomCenter,
                  clipBehavior: Clip.none,
                  children: [
                    homeController.hostDetail.value.hostProfile?.bannerUrl!=null?  Widgets.networkImage(
                        homeController.hostDetail.value.hostProfile?.bannerUrl ?? "",
                      height: .2.sh, // Set a fixed height for the cover image
                      width: double.infinity,

                    ) :Image.asset(
                      Assets.coverImg,
                      fit: BoxFit.cover, // Ensure the image covers the area
                      height: .2.sh, // Set a fixed height for the cover image
                      width: double.infinity,
                    ),
                    Positioned(
                        top: 50,
                        left: 20,
                        child: GestureDetector(
                            onTap: () {
                              Get.back();
                            },
                            child: const Icon(
                              Icons.arrow_back_ios_new_outlined,
                              size: 20,
                              color: Colors.white,
                            ))),
                    Positioned(
                        bottom: -50,
                        child:   AdvancedAvatar(
                          animated: true,
                          size: 120,
                          foregroundDecoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.grey,
                              width: 0.0,
                            ),
                          ),
                          child: Widgets.networkImage(
                              homeController.hostDetail.value.imageUrl ?? "",
                              width: 200,
                              height: 200),
                        ),),
                  ],
                ),
                Padding(
                  padding:
                  PaddingConstants.screenPadding.copyWith(top: 0, bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Widgets.heightSpaceH5,
                      Widgets.heightSpaceH2,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Widgets.widthSpaceW4,
                          Texts.textBlock( "${homeController.hostDetail.value.firstName}", size: 18),
                          // Image.asset(
                          //   Assets.levelbadge,
                          //   height: 15,
                          //   width: 15,
                          // )
                        ],
                      ),
                      const SizedBox(
                        height: 2,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            Assets.reviewStar,
                            height: 10,
                            width: 10,
                          ),
                          Widgets.widthSpaceW1,
                          Texts.textNormal("${ homeController.hostDetail.value.hostProfile?.ratings ?? "0"} (${homeController.hostDetail.value.hostProfile?.reviewsCount ?? "0"} review(s))",
                              size: 11, color: Colors.black54),
                        ],
                      ),
                      Widgets.heightSpaceH2,
                    widget.fromChat==true?CustomButton(
                      label: "Pay Deposit",
                      fontSize: 12,
                      borderColor: Colors.transparent,
                      backgroundColor: ColorConstants.primaryColor,
                      textColor: ColorConstants.blackColor,
                      radius: 50,
                      onTap: () {

                        Get.to(()=>PayDepositView());
                      },
                    ):  Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Expanded(
                            child: CustomButton(
                              label: "Chat",
                              fontSize: 12,
                              backgroundColor: ColorConstants.blackColor,
                              textColor: ColorConstants.whiteColor,
                              radius: 50,
                              onTap: () {


                                final existingChat =
                                Get.find<ChatController>()
                                    .chats
                                    .firstWhereOrNull((chat) =>
                                chat.otherUser?.id ==
                                    homeController.hostDetail.value.id);
                                existingChat == null
                                    ? Get.to(() => ChatView(
                                  chatUser: OtherUser(
                                    id: homeController.hostDetail.value.id,
                                    name: "${homeController.hostDetail.value.firstName}",
                                    image:homeController.hostDetail.value.imageUrl,
                                  ),
                                ))?.then((value) {

                                })
                                    : Get.to(() => ChatView(
                                  chatId:
                                  existingChat.chatId.toString(),
                                  chatUser: OtherUser(
                                    id: homeController.hostDetail.value.id,
                                    name: "${homeController.hostDetail.value.firstName} ${homeController.hostDetail.value.lastName}",
                                    image:homeController.hostDetail.value.imageUrl,
                                  ),
                                ))?.then((value) {


                                });
                              },
                            ),
                          ),
                          Widgets.widthSpaceW3,
                          Expanded(
                            child: CustomButton(
                              label: "Pay Deposit",
                              fontSize: 12,
                              borderColor: Colors.transparent,
                              backgroundColor: ColorConstants.primaryColor,
                              textColor: ColorConstants.blackColor,
                              radius: 50,
                              onTap: () {

                                Get.to(()=>PayDepositView());
                              },
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Divider(
                  color: ColorConstants.greyColor,
                ),
                Padding(
                  padding: PaddingConstants.screenPadding.copyWith(top: 3),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock("About Me", size: 16),
                      Widgets.heightSpaceH1,
                      ReadMoreText(
                        homeController.hostDetail.value.hostProfile?.bio ?? "",
                        trimLines: 6,

                        trimMode: TrimMode.Line,
                        trimCollapsedText: 'Show more',
                        trimExpandedText: 'Show less',
                        lessStyle: TextStyle(
                            fontSize: 13,
                            color: Colors.black),
                        moreStyle: TextStyle(
                            fontSize: 13,
                            color:  Colors.black),
                        style: const TextStyle(
                            fontSize: 13, color: Colors.black,fontFamily: "InstrumentSansRegular",fontWeight: FontWeight.w400),
                      ),

                      Widgets.heightSpaceH2,
                      Divider(
                        color: ColorConstants.greyColor,
                      ),
                      Widgets.heightSpaceH1,
                      buildGeneralInfo(),
                      Divider(
                        color: ColorConstants.greyColor,
                      ),
                      Widgets.heightSpaceH1,

                      Widgets.heightSpaceH1,
                      homeController.hostDetail.value.hostProfile?.videoInterviewUrl != null
                          ? Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              final videoUrl = homeController.hostDetail.value.hostProfile?.videoInterviewUrl ?? "";
                              if (videoUrl.contains('youtube.com') || videoUrl.contains('youtu.be')) {
                                // Launch YouTube video in browser or YouTube app
                                launchUrl(
                                  Uri.parse(videoUrl),
                                  mode: LaunchMode.externalApplication,
                                );
                              } else {
                                // Play regular video in VideoPlayerScreen
                                Get.to(() => VideoPlayerScreen(videoUrl: videoUrl));
                              }
                            },
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: SizedBox(
                                    height: 140,
                                    width: 1.sw,
                                    child: homeController.hostDetail.value.hostProfile!.videoInterviewUrl!.contains('youtube.com') || homeController.hostDetail.value.hostProfile!.videoInterviewUrl!.contains('youtu.be')
                                        ? Builder(
                                        builder: (context) {

                                          final videoId =getYouTubeVideoId(homeController
                                              .hostDetail
                                              .value
                                              .hostProfile
                                              ?.videoInterviewUrl??"");
                                          final thumbnailUrl = videoId != null
                                              ? 'https://img.youtube.com/vi/$videoId/0.jpg'
                                              : null;

                                          return thumbnailUrl!=null? Widgets.networkImage(
                                            thumbnailUrl??"",
                                          ):Container(
                                            color: Colors.black87,
                                            child: Column(
                                              mainAxisAlignment:
                                              MainAxisAlignment
                                                  .center,
                                              children: [
                                                Icon(
                                                  CupertinoIcons
                                                      .play_rectangle_fill,
                                                  color:
                                                  ColorConstants
                                                      .splash,
                                                  size: 40,
                                                ),
                                                SizedBox(height: 8),
                                                Text(
                                                  'YouTube Video',
                                                  style: TextStyle(
                                                    color:
                                                    ColorConstants
                                                        .splash,
                                                    fontWeight:
                                                    FontWeight
                                                        .bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }
                                    )
                                        : Widgets.networkImage(
                                      homeController.hostDetail.value.hostProfile?.videoThumbnail ?? "",
                                    ),
                                  ),
                                ),
                                CircleAvatar(
                                  backgroundColor: ColorConstants.splash,
                                  child: Icon(
                                    CupertinoIcons.play_arrow_solid,
                                    color: Colors.black,
                                    size: 24,
                                  ),
                                  radius: 20,
                                ),
                              ],
                            ),
                          ),
                          Widgets.heightSpaceH2,
                        ],
                      )
                          : SizedBox(),
                      buildHostVerificationInfo(),
                      Divider(
                        color: ColorConstants.greyColor,
                      ),
                      buildCertificatesTrainings(),
                      Widgets.heightSpaceH2,
                      Divider(
                        color: ColorConstants.greyColor,
                      ),     Widgets.heightSpaceH1,
                      Texts.textBlock("Reviews", size: 16),
                      Widgets.heightSpaceH2,
                      if (homeController.hostDetail.value.ratings?.isEmpty ?? true)
                        Center(
                          child: Texts.textNormal(
                            "No reviews yet",
                            size: 12,
                            color: ColorConstants.textColor,
                          ),
                        )
                      else
                        ListView.separated(padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: homeController.hostDetail.value.ratings!.length >3 ?3:homeController.hostDetail.value.ratings?.length ?? 0,
                          separatorBuilder: (context, index) => Widgets.heightSpaceH3,
                          itemBuilder: (context, index) {
                            host.UserRatings? rating = homeController.hostDetail.value.ratings?[index];
                            return ReviewSection(
                              name: "${rating?.user?.firstName??""}" ?? "",
                              image: rating?.user?.imageUrl ?? "",
                              dateAndTime: rating?.date ?? "",
                              comment: rating?.review ?? "",
                              rating: rating?.rating?.toDouble() ?? 0,
                              images:rating!.images,
                            );
                          },
                        ),
                      Widgets.heightSpaceH3,
                      if (homeController.hostDetail.value.hostProfile!.reviewsCount! >0)
                        homeController.hostDetail.value.hostProfile!.reviewsCount! >3? CustomButton(
                          label: "See All Reviews",
                          textColor: ColorConstants.blackColor,
                          radius: 50,
                          borderColor: ColorConstants.blackColor,
                          onTap: () {homeController.fetchReviews(page: 1);
                          Get.to(() => TravellerReviewView());
                          },
                        ):SizedBox(),
                    ],
                  ),
                )
              ],
            ),
          ):SizedBox();
        }
      ),
    );
  }

  buildGeneralInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textBlock("General Information", size: 16),
        Widgets.heightSpaceH1,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "I live in",
                    style: TextStyle(
                        fontSize: 10,
                        color: ColorConstants.textColor,
                        fontFamily: "InstrumentSansRegular"),
                  ),
                  SizedBox(height: 2),
                  Texts.textNormal(homeController.hostDetail.value.hostProfile?.country??"",
                      size: 13, color: ColorConstants.blackColor),
                ],
              ),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "City",
                    style: TextStyle(
                        fontSize: 10,
                        color: ColorConstants.textColor,
                        fontFamily: "InstrumentSansRegular"),
                  ),
                  SizedBox(height: 2),
                  Texts.textNormal(homeController.hostDetail.value.hostProfile?.city??"",
                      size: 13, color: ColorConstants.blackColor),
                ],
              ),
            ),

          ],
        ),
        Widgets.heightSpaceH2,
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Language",
                    style: TextStyle(
                        fontSize: 10,
                        color: ColorConstants.textColor,
                        fontFamily: "InstrumentSansRegular"),
                  ),
              
                  const SizedBox(height: 3),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...(homeController.hostDetail.value.hostProfile?.languages ?? []).map(
                            (country) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Texts.textNormal(
                            country,
                            size: 13,
                            color: ColorConstants.blackColor,
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ).toList(),
                    ],
                  ),
              
                ],
              ),
            ),    Widgets.heightSpaceH1,
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Services in",
                    style: TextStyle(
                        fontSize: 10,
                        color: ColorConstants.textColor,
                        fontFamily: "InstrumentSansRegular"),
                  ),
                  const SizedBox(height: 3),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...(homeController.hostDetail.value.hostProfile?.offeredCountries ?? []).map(
                        (country) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            children: [
                              Text(
                                Data.getFlag(country),
                                style: TextStyle(
                                  fontSize: 9,
                                  color: ColorConstants.blackColor,
                                  fontFamily: "InstrumentSansRegular",
                                ),
                              ),SizedBox(width: 3),
                              Texts.textNormal(
                                country,
                                size: 13,
                                color: ColorConstants.blackColor,
                                textAlign: TextAlign.start,
                              ),
                            ],
                          ),
                        ),
                      ).toList(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        Widgets.heightSpaceH1,
      ],
    );
  }

  buildHostVerificationInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textBlock("Host Verification Methods", size: 16),
        Widgets.heightSpaceH2,
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
           Row(children: [Image.asset(
              Assets.smsIcons,
              height: 15,
              width: 15,
            ),
              Widgets.widthSpaceW2,
              Texts.textNormal("Email Address", size: 12),],),

            homeController.hostDetail.value.hostProfile?.emailVerified==0?Image.asset(
              Assets.not_verifiedIcon,
              height: 15,
              width: 15,
            ):Image.asset(
              Assets.verifiedIcon,
              height: 15,
              width: 15,
            )
          ],
        ),
        Widgets.heightSpaceH2,
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(children: [Image.asset(
              Assets.passportIcon,
              height: 15,
              width: 15,
            ),
              Widgets.widthSpaceW2,
              Texts.textNormal("Passport/IDCard", size: 12),],),

            homeController.hostDetail.value.hostProfile?.passportVerified==0?Image.asset(
              Assets.not_verifiedIcon,
              height: 15,
              width: 15,
            ):Image.asset(
              Assets.verifiedIcon,
              height: 15,
              width: 15,
            )
          ],
        ),
        Widgets.heightSpaceH2,
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
           Row(children: [ Image.asset(
             Assets.videoCallIcon,
             height: 15,
             width: 15,
           ),
             Widgets.widthSpaceW2,
             Texts.textNormal("Video Interview", size: 12),],),

            homeController.hostDetail.value.hostProfile?.videoVerification==0?Image.asset(
              Assets.not_verifiedIcon,
              height: 15,
              width: 15,
            ):Image.asset(
              Assets.verifiedIcon,
              height: 15,
              width: 15,
            )
          ],
        ),
        Widgets.heightSpaceH2,
      ],
    );
  }
  buildCertificatesTrainings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Widgets.heightSpaceH1,
        Texts.textBlock("Certificates And Trainings", size: 16),
        Widgets.heightSpaceH2,
        homeController.hostDetail.value.hostProfile?.certificateOfIncorporationUrl != null &&
            homeController.hostDetail.value.hostProfile!.certificateOfIncorporationUrl!.isNotEmpty
            ? ListView.separated(
          shrinkWrap: true,padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: homeController.hostDetail.value.hostProfile!.certificateOfIncorporationUrl?.length??0,
          separatorBuilder: (context, index) => Widgets.heightSpaceH2,
          itemBuilder: (context, index) {
          host.Certificate? certificate = homeController.hostDetail.value.hostProfile?.certificateOfIncorporationUrl?[index];
          return InkWell(
            onTap: certificate?.certificateUrl != null
                ? () async {
              try {
                if (certificate?.certificateUrl == null ||
                    certificate!.certificateUrl!.isEmpty) {
                  Widgets.showSnackBar(
                      'Error', 'File URL not available');
                  return;
                }

                // Show download progress
                Widgets.showLoader('Downloading...');

                // Get the downloads directory
                final Directory? downloadsDir =
                await getExternalStorageDirectory();
                if (downloadsDir == null) {
                  Widgets.hideLoader();
                  Widgets.showSnackBar('Error',
                      'Could not access downloads folder');
                  return;
                }

                final String fileName = certificate
                    .certificateName ??
                    'document_${DateTime.now().millisecondsSinceEpoch}${certificate.certificateUrl?.split('.').last}';
                final String filePath =
                    '${downloadsDir.path}/$fileName';

                // Download the file
                final response = await http
                    .get(Uri.parse(certificate.certificateUrl!));
                if (response.statusCode == 200) {
                  final File file = File(filePath);
                  await file.writeAsBytes(response.bodyBytes);

                  Widgets.hideLoader();
                  Widgets.showSnackBar('Success',
                      'File downloaded to Downloads folder');

                  // Open the file
                  await OpenFile.open(filePath);
                } else {
                  Widgets.hideLoader();
                  Widgets.showSnackBar(
                      'Error', 'Failed to download file');
                }
              } catch (e) {
                Widgets.hideLoader();
                Widgets.showSnackBar(
                    'Error', 'Failed to download: $e');
              }
            }
                : null,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(
                  Assets.certificateIcon,
                  height: 15,
                  width: 15,
                ),
                Widgets.widthSpaceW1,
                Expanded(
                  child: Texts.textBlock(
                    certificate?.certificateName ?? "",
                    size: 15,
                    fontWeight: FontWeight.w400,
                    color: ColorConstants.blackColor,
                    align: TextAlign.start,
                  ),
                ),
                Widgets.widthSpaceW1,
                Icon(
                  Icons.download,
                  size: 16,
                  color: Colors.black54,
                )
              ],
            ),
          );
          },
        )
            : Texts.textNormal("No certificates available", size: 12),
      ],
    );
  }

}
