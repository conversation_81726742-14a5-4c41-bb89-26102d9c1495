import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';

import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../../model/notifications_model.dart';
import '../controller/home_controller.dart';

class TravellerNotificationsView extends StatefulWidget {
  const TravellerNotificationsView({super.key});

  @override
  State<TravellerNotificationsView> createState() => _TravellerNotificationsViewState();
}

class _TravellerNotificationsViewState extends State<TravellerNotificationsView> {
  late TravellerHomeController homeController;
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    homeController = Get.find();

    scrollController.addListener(scrollListener);
  }

  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        homeController.totalNotifications.value >
            homeController.notifications.length) {
      homeController.fetchNotifications(
          page: homeController.currentNotificationPage.value + 1);
      homeController
          .currentNotificationPage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "Notifications"),
      body: buildNotifications(),
    );
  }

  Widget buildNotifications() {
    return Obx(
      () {
        return ListView(
          shrinkWrap: true,
          physics:
              const BouncingScrollPhysics(), // Since parent is scrollable
          controller: scrollController,
          children: [
            homeController.isNotificationLoading.value
                ? Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: const ShimmerListSkeleton(),
                )
                : homeController.notifications.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          Notifications notifications =
                              homeController.notifications[index];
                          return Widgets.buildNotificationItem(
                              title: notifications.title ?? "",
                              description: notifications.message ?? "",
                              time: notifications.formattedCreatedAt ?? "");
                        },
                        separatorBuilder: (context, index) {
                          return Divider(thickness: 0.8,color: ColorConstants.greyColor,);
                        },
                        itemCount: homeController.notifications.length ?? 0)
                    : Widgets.noRecordsFound(title: "No notifications"),
            if (homeController.isNotificationMoreLoading.value)
              Widgets.moreLoading(),
          ],
        );
      },
    );
  }


}
