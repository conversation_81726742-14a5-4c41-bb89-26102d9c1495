class TravellerHost {
  int? id;
  String? firstName;
  String? lastName;
  String? longitude;
  String? latitude;
  String? image;
  int? profileId;
  int? userId;
  String? ratings;
  int? reviewsCount;
  String? bio;
  String? country;
  String? city; String? distance;
  String? gender;
  List<String>? languages;
  List<String>? offeredCountries;
  String? imageUrl;

  TravellerHost({
    this.id,
    this.firstName,this.distance,
    this.lastName,
    this.longitude,
    this.latitude,
    this.image,
    this.profileId,
    this.userId,
    this.ratings,
    this.reviewsCount,
    this.bio,
    this.country,
    this.city,
    this.gender,
    this.languages,
    this.offeredCountries,
    this.imageUrl,
  });

  TravellerHost.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    longitude = json['longitude'];    distance = json['distance'].toString();
    latitude = json['latitude'];
    image = json['image'];
    profileId = json['profile_id'];
    userId = json['user_id'];
    ratings = json['ratings'];
    reviewsCount = json['reviews_count'];
    bio = json['bio'];
    country = json['country'];
    city = json['city'];
    gender = json['gender'];
    languages = json['languages'].cast<String>();
    offeredCountries = json['offered_countries'].cast<String>();
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['image'] = this.image;
    data['profile_id'] = this.profileId;
    data['user_id'] = this.userId;
    data['ratings'] = this.ratings;
    data['reviews_count'] = this.reviewsCount;
    data['bio'] = this.bio;
    data['country'] = this.country;
    data['city'] = this.city;
    data['gender'] = this.gender;
    data['languages'] = this.languages;
    data['offered_countries'] = this.offeredCountries;
    data['image_url'] = this.imageUrl;
    return data;
  }
}
