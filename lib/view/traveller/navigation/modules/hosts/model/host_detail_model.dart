

import '../../../../../host/bottom_nav_bar/modules/booking/model/booking_detail_model.dart';
import '../../booking/model/booking_detail_model.dart';

class HostDetail {
  int? id;
  String? firstName  ;String? lastName;
  String? email;
  int? isVerified;
  int? role;
  String? fcmToken;
  String? image;
  String? longitude;
  String? latitude;
  String? createdAt;
  String? updatedAt;
  String? imageUrl;
  HostProfile? hostProfile;
  List<UserRatings>? ratings;
  String? countryCode;
  HostDetail(
      {this.id,
        this.firstName,this.lastName,
        this.email,this.countryCode,
        this.isVerified,
        this.role,
        this.fcmToken,
        this.image,
        this.longitude,
        this.latitude,
        this.createdAt,
        this.updatedAt,
        this.imageUrl,
        this.hostProfile,
        this.ratings});

  HostDetail.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];lastName = json['last_name'];
    countryCode = json['country_code'];
    email = json['email'];
    isVerified = json['is_verified'];
    role = json['role'];
    fcmToken = json['fcm_token'];
    image = json['image'];
    longitude = json['longitude'];
    latitude = json['latitude'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    imageUrl = json['image_url'];
    hostProfile = json['host_profile'] != null
        ? new HostProfile.fromJson(json['host_profile'])
        : null;
    if (json['ratings'] != null) {
      ratings = <UserRatings>[];
      json['ratings'].forEach((v) {
        ratings!.add(new UserRatings.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;

    data['email'] = this.email;
    data['is_verified'] = this.isVerified;
    data['role'] = this.role;
    data['fcm_token'] = this.fcmToken;
    data['image'] = this.image;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['image_url'] = this.imageUrl;
    if (this.hostProfile != null) {
      data['host_profile'] = this.hostProfile!.toJson();
    }
    if (this.ratings != null) {
      data['ratings'] = this.ratings!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HostProfile {
  int? id;
  int? userId;
  String? bio;String? bannerUrl;String? videoUrl;
  String? ratings;
  int? reviewsCount;
  String? country;
  String? city;
  String? dob;
  String? gender;
  List<String>? languages;
  List<String>? offeredCountries;
  String? phone;
  String? passportIdCard;
  String? videoInterview;
  String? certificateOfIncorporation;
  String? tourOperatorLicense;
  String? createdAt;
  String? updatedAt;
  String? passportIdCardUrl;
  String? videoInterviewUrl;
  List<Certificate>? certificateOfIncorporationUrl;
  String? tourOperatorLicenseUrl;
  int? emailVerified;
  int? phoneVerified;
  int? passportVerified;
  int? videoVerification;
  String? videoThumbnail;
  HostProfile(
      {this.id,
        this.userId,
        this.bio,
        this.ratings,
        this.reviewsCount,
        this.country,this.videoThumbnail,
        this.city,
        this.gender,
        this.languages,
        this.offeredCountries,this.dob,
        this.phone,
        this.passportIdCard,
        this.videoInterview,this.bannerUrl,this.videoUrl,
        this.certificateOfIncorporation,
        this.tourOperatorLicense,
        this.createdAt,
        this.updatedAt,
        this.passportIdCardUrl,
        this.videoInterviewUrl,this.emailVerified,this.phoneVerified,this.passportVerified,this.videoVerification,
        this.certificateOfIncorporationUrl,
        this.tourOperatorLicenseUrl});

  HostProfile.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];  dob = json['dob'];
    emailVerified = json['email_verified'];
    videoVerification= json['video_interview_verified'];
    passportVerified = json['id_card_verified'];
   phoneVerified = json['phone_verified'];
    bio = json['bio'];
    ratings = json['ratings'];
    reviewsCount = json['reviews_count'];
    country = json['country'];

    city = json['city'];
    gender = json['gender'];
    languages = json['languages'].cast<String>();
    offeredCountries = json['offered_countries'].cast<String>();
    phone = json['phone'];
    passportIdCard = json['passport_id_card'];
    videoInterview = json['video_interview'];
    videoThumbnail = json['video_thumbnail_url'];

    tourOperatorLicense = json['tour_operator_license'];
    createdAt = json['created_at'];
    bannerUrl = json['banner_image_url'];videoUrl= json['video_interview_url'];
    updatedAt = json['updated_at'];
    passportIdCardUrl = json['passport_id_card_url'];
    videoInterviewUrl = json['video_interview_url'];
    tourOperatorLicenseUrl = json['tour_operator_license_url'];
    if (json['certificates'] != null) {
      certificateOfIncorporationUrl = <Certificate>[];
      json['certificates'].forEach((v) {
        certificateOfIncorporationUrl!.add(Certificate.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['bio'] = this.bio;
    data['ratings'] = this.ratings;
    data['reviews_count'] = this.reviewsCount;
    data['country'] = this.country;
    data['city'] = this.city;
    data['gender'] = this.gender;
    data['languages'] = this.languages;
    data['offered_countries'] = this.offeredCountries;
    data['phone'] = this.phone;
    data['passport_id_card'] = this.passportIdCard;
    data['video_interview'] = this.videoInterview;
    data['tour_operator_license'] = this.tourOperatorLicense;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['passport_id_card_url'] = this.passportIdCardUrl;
    data['video_interview_url'] = this.videoInterviewUrl;

    data['tour_operator_license_url'] = this.tourOperatorLicenseUrl;
    return data;
  }
}

class UserRatings {
  int? id;
  int? guideUserId;
  int? userId;
  int? rating;
  String? review;String? date;
  User? user;
  List<RatingImage>? images;

  UserRatings(
      {this.id,
        this.guideUserId,
        this.userId,
        this.rating,this.date,
        this.review,
        this.user,
        this.images});

  UserRatings.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    guideUserId = json['guide_user_id'];
    userId = json['user_id'];
    rating = json['rating'];    date = json['formatted_created_at'];
    review = json['review'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
    if (json['images'] != null) {
      images = <RatingImage>[];
      json['images'].forEach((v) {
        images!.add(RatingImage.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['guide_user_id'] = this.guideUserId;
    data['user_id'] = this.userId;
    data['rating'] = this.rating;
    data['review'] = this.review;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    if (this.images != null) {
      data['images'] = this.images!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Certificate {
  int? id;
  String? certificateName;
  String? certificateUrl;

  Certificate({this.id, this.certificateName, this.certificateUrl});

  Certificate.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    certificateName = json['certificate_name'];
    certificateUrl = json['certificate_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['certificate_name'] = this.certificateName;
    data['certificate_url'] = this.certificateUrl;
    return data;
  }
}

