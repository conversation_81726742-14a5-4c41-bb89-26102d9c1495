import 'dart:convert';

import 'package:country_picker/country_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:indyguide/controller/user_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image_cropper/image_cropper.dart';

import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/services/http_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../../../../model/user_model.dart';
import 'package:http/http.dart' as http;
class ProfileController extends GetxController

{
  String? imagePath;

  late UserController userController;
  TextEditingController firstNameController = TextEditingController();
  TextEditingController usernameController = TextEditingController();
  RxString selectedCountry = "".obs;
  TextEditingController phoneController = TextEditingController();
  RxString selectedCountryCode = "1".obs;
  TextEditingController socialMediaLink = TextEditingController();
  TextEditingController lastNameController = TextEditingController();

  TextEditingController emailController = TextEditingController();
  @override
  void onInit() {
    userController=Get.find();


    super.onInit();
  }


  changePassword(String password, String oldPassword) async {
    Widgets.showLoader("Loading..");

    try {
      var payload = {
        "new_password": password,
        "old_password": oldPassword
      };
      var response =
      await ApiService.postData(Endpoints.updatePassword, payload);

      Widgets.hideLoader();
      if (response.status == true) {
        Get.back();
        Widgets.showSnackBar("Success", response.message.toString());
      } else {
        Widgets.showSnackBar("Error", response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", e.toString());
    }
  }

  pickCountryBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "Search Country")),
      showPhoneCode: false, // Set to true if you need phone codes
      onSelect: (Country country) {
        selectedCountry.value = country.name;

      },
    );
  }
  pickCountryCodeBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "Search Country")),
      showPhoneCode: true, // Set to true if you need phone codes
      onSelect: (Country country) {print(country.phoneCode);
        selectedCountryCode.value = country.phoneCode;

      },
    );
  }
  updateUserProfile() async {
    try {
      if (firstNameController.text.isEmpty) {
        return Widgets.showSnackBar("Alert", "First name is empty");
      }
      if (lastNameController.text.isEmpty) {
        return Widgets.showSnackBar("Alert", "Last name is empty");
      }

      Widgets.showLoader("Saving Profile...");
      var data = {
        'email': emailController.text,
        'first_name': firstNameController.text,"last_name":lastNameController.text,
        "phone":phoneController.text,   "instagram_link":socialMediaLink.text,
        'country': selectedCountry.value,"country_code":selectedCountryCode.value,

      };
      var response = await ApiService.postData(Endpoints.updateProfile, data);
      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");
        UserModel userModel = UserModel.fromJson(response.data['user']);

        await userController.saveUser(userModel, userController.token ?? "",false);
        await userController.fetchUser();
        update();
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }
  void pickImage(ImageSource image) async {
    try {
      XFile? file = await ImagePicker().pickImage(
        source: image,
        // You can adjust other parameters like maxWidth and maxHeight if needed
      );

      if (file != null) {
        // First, allow user to crop the image
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: file.path,
          aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1), // Square aspect ratio for profile
          compressQuality: 70,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: 'Crop Image',
              toolbarColor: ColorConstants.primaryColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.square,
              lockAspectRatio: true,
            ),
            IOSUiSettings(
              title: 'Crop Image',
              aspectRatioLockEnabled: true,
              resetAspectRatioEnabled: false,
            ),
          ],
        );

        if (croppedFile != null) {
          // Then compress the cropped image
          final directory = await getTemporaryDirectory();
          String newPath = '${directory.path}/compressed_image${Utils.generateUniqueNumber()}.jpg';

          var result = await FlutterImageCompress.compressAndGetFile(
            croppedFile.path,
            newPath,
            quality: 50,
          );

          if (result != null) {
            imagePath = result.path;
            update();
            await changeProfilePic();
          }
        }
      }
    } catch (e) {
      Widgets.showSnackBar("Error", "Failed to process image");
    }
  }


  changeProfilePic() async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateProfileImage}'));
    var pic = await http.MultipartFile.fromPath('image', imagePath ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {
        UserModel userModel = UserModel.fromJson(decodedData['user']);
        await userController.saveUser(userModel, userController.token ?? "",false);
        userController.fetchUser();
        Widgets.showSnackBar("Success", "Image Updated Successfully");
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }
  void showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          child: Container(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textBlock("Select an action",
                        align: TextAlign.center),
                    GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.clear,
                          color: Colors.black54,
                        ))
                  ],
                ),
                Widgets.heightSpaceH2,
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.camera_alt_outlined),
                  title: Text("Camera"),
                  onTap: () {
                    Get.back();
                    pickImage(ImageSource.camera);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.photo),
                  title: Text("Gallery"),
                  onTap: () {
                    Get.back();
                  pickImage(ImageSource.gallery);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.cancel_outlined),
                  title: Text("Cancel"),
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }


}