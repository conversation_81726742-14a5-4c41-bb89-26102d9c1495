import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';

import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/custom_dropdown.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/profile_controller.dart';



class TravellerEditProfile extends StatefulWidget {
  const TravellerEditProfile({super.key});

  @override
  State<TravellerEditProfile> createState() => _TravellerEditProfileState();
}

class _TravellerEditProfileState extends State<TravellerEditProfile> {

  late ProfileController profileController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    profileController = Get.find();
    profileController.lastNameController.text= profileController.userController.userModel?.lastName??"";

    profileController.firstNameController.text= profileController.userController.userModel?.firstName??"";
    profileController.emailController.text= profileController.userController.userModel?.email??"";
    profileController.socialMediaLink.text= profileController.userController.userModel?.instagramLink??"";
    profileController.phoneController.text= profileController.userController.userModel?.phone??"";
    profileController.selectedCountry.value= profileController.userController.userModel?.country??"";
    profileController.selectedCountryCode.value= profileController.userController.userModel?.countryCode??"";


  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: Widgets.customAppBar(title: "Edit Profile"),
        body: SingleChildScrollView(
            padding: PaddingConstants.screenPaddingLess,
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

                  GetBuilder(
                      init: profileController,
                      builder: (_) {
                        return Center(
                          child: Stack(
                            alignment: Alignment.center,
                            clipBehavior: Clip.none,
                            children: [
                              profileController.imagePath != null
                                  ? AdvancedAvatar(
                                animated: true,
                                size: 120,
                                image: FileImage(
                                    File(profileController.imagePath ?? "")),
                                foregroundDecoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ColorConstants.primaryColor,
                                    width: 0.0,
                                  ),
                                ),
                              )
                                  : AdvancedAvatar(
                                animated: true,
                                size: 120,
                                image: NetworkImage(profileController
                                    .userController.userModel?.imageUrl ??
                                    ""),
                                foregroundDecoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: ColorConstants.primaryColor,
                                    width: 0.0,
                                  ),
                                ),
                              ),
                              Positioned(
                                  right: 16,
                                  bottom: -4,
                                  child: InkWell(
                                      onTap: () {
                                        profileController.showImageSourceDialog(context);
                                      },
                                      child:CircleAvatar(
                                          radius: 15,
                                          backgroundColor: ColorConstants.primaryColor,
                                          child: const Icon(
                                            Icons.camera_alt,
                                            size: 15,

                                          )))),
                            ],
                          ),
                        );
                      }),





                  Widgets.heightSpaceH2,
                  EntryField(
                    controller: profileController.firstNameController,
                    label: "First Name", hint: "write here",textCapitalization: TextCapitalization.words,
                    autoFillType: const [AutofillHints.name],
                    // prefixIcon: Assets.mailIcon,
                  ), EntryField(
                    controller: profileController.lastNameController,
                    label: "Last Name", hint: "write here",textCapitalization: TextCapitalization.words,
                    autoFillType: const [AutofillHints.name],
                    // prefixIcon: Assets.mailIcon,
                  ),
                  EntryField(readOnly: true,
                    label: "Email", hint: "write here",
                    controller: profileController.emailController,

                    // prefixIcon: Assets.mailIcon,
                  ),

                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Obx(() => EntryField(
                          readOnly: true,
                          onTap: () => profileController.pickCountryCodeBottomSheet(context),
                          controller: TextEditingController(
                            text: profileController.selectedCountryCode.value.isNotEmpty
                              ? "+${profileController.selectedCountryCode.value}"
                              : "+1"
                          ),
                          label: "Code",
                          hint: "+1",
                          textInputType: TextInputType.phone,
                        )),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        flex: 8,
                        child: EntryField(
                          controller: profileController.phoneController,
                          label: "Phone Number",
                          hint: "write here",
                          textInputType: TextInputType.phone,
                          autoFillType: const [AutofillHints.telephoneNumber],
                        ),
                      ),
                    ],
                  ),

                  Obx(
                        () => CustomDropdown(
                        onTap: () {
                          profileController.pickCountryBottomSheet(context);
                        },
                        value: profileController.selectedCountry.value == ""
                            ? null
                            : profileController.selectedCountry.value,
                        hint: "--Select--",
                        label: "Country"),
                  ),

                  EntryField(
                    controller: profileController.socialMediaLink,
                    label: "Social media link", hint: "write here",

                    // prefixIcon: Assets.mailIcon,
                  ),
                  Widgets.heightSpaceH5,
                  CustomButton(
                    label: "Save Changes",
                    textColor: ColorConstants.blackColor,
                    backgroundColor: ColorConstants.primaryColor,
                    radius: 50,
                    onTap: () {

                      profileController.updateUserProfile();
                    },
                  ),

                ]))
    );
  }


}
