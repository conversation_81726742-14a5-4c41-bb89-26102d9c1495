import 'package:flutter/material.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';

import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/widgets/widgets.dart';





class TravellerNotificationsScreen extends StatelessWidget {
  const TravellerNotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "Notifications"),

      body: ListView(

        children: [
          buildNotificationItem(
            title: "Trip Request Created",
            description: "Your trip_requests request from Osaka to Tokyo has been posted.",
            time: "11 Feb, 2024 - 10:30 PM",
          ),Divider(thickness: 0.8,color: ColorConstants.greyColor,),
          buildNotificationItem(
            title: "Your Trip Starts Soon!",
            description:
            "Your trip_requests to Bali starts in 3 days! Don't forget to finalize your plans and confirm with your host.",
            time: "11 Feb, 2024 - 9:00 PM",
          ),Divider(thickness: 0.8,color: ColorConstants.greyColor,),
          buildNotificationItem(
            title: "Trip Request Created",
            description: "Your trip_requests request from Osaka to Tokyo has been posted.",
            time: "11 Feb, 2024 - 8:30 PM",
          ),
        ],
      ),
    );
  }

  Widget buildNotificationItem({
    required String title,
    required String description,
    required String time,
  }) {
    return ListTile(
      leading: CircleAvatar(
        radius: 20,
        backgroundColor:ColorConstants.selectedNavIcon,
        child: Image.asset( Assets.notificationSettingIcon,height: 20,width: 20,color: Colors.white,),
      ),

      title:Texts.textBlock(title,fontWeight: FontWeight.w600,size: 13,),

      subtitle: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Widgets.heightSpaceH05,
          Texts.textNormal(description,textAlign: TextAlign.start,size: 12,color: ColorConstants.fullBlackColor),

          Widgets.heightSpaceH1,
          Text(
            time ?? "",
            style: const TextStyle(
                fontSize: 10,
                color: Colors.black54,
                fontFamily: "InstrumentSansRegular"),
          ),
        ],
      ),
    );
  }
}


