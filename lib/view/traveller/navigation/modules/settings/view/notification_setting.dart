import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';

import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/notification_controller.dart';

class TravellerNotificationSettingsView extends StatelessWidget {
  TravellerNotificationSettingsView({super.key});

  final controller = Get.put(TravellerNotificationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: Widgets.customAppBar(title: "Notifications Settings"),
        body: SingleChildScrollView(
          child: Padding(
            padding: PaddingConstants.screenPadding.copyWith(top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Get notified about your trip request updates, including creation, modifications, and status changes',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.bookingUpdates.value ?? false,
                              onChanged: (v) {
                                controller.bookingUpdates.value = v;
                                controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          "Stay informed about payment activities, including successful transactions and required actions",
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.paymentNotifications.value ??
                                  false,
                              onChanged: (v) {
                                controller.paymentNotifications.value = v;
                                controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Receive reminders about your upcoming trips and important travel updates',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.tripAlerts.value ?? false,
                              onChanged: (v) {
                                controller.tripAlerts.value = v;
                                controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Get notified when guides respond to your messages',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value: controller.messageNotifications.value ??
                                  false,
                              onChanged: (v) {
                                controller.messageNotifications.value = v;
                                controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Receive instant notifications on your mobile device',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value:
                                  controller.pushNotifications.value ?? false,
                              onChanged: (v) {
                                controller.pushNotifications.value = v;
                                controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
                Widgets.heightSpaceH2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Texts.textBlock(
                          'Receive important updates via email',
                          size: 14,
                          maxline: 3,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => Transform.scale(
                          scale: .7,
                          child: CupertinoSwitch(
                              activeColor: ColorConstants.splash,
                              value:
                                  controller.emailNotifications.value ?? false,
                              onChanged: (v) {
                                controller.emailNotifications.value = v;
                                controller.updateNotificationSetting();
                              })),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
