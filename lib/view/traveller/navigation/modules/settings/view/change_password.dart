import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';

import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/profile_controller.dart';

class TravellerChangePassword extends StatelessWidget {
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController oldPasswordController = TextEditingController();

  ProfileController profileController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "Change Password"),
      body: Padding(
        padding: PaddingConstants.screenPaddingLess,
        child: Column(
          children: [
            Widgets.heightSpaceH3,
            EntryField(
                controller: oldPasswordController,
                label: "Current Password",
                hint: "write here"
                // controller: email,

                // prefixIcon: Assets.mailIcon,
                ),
            EntryField(
              label: "New Password", hint: "write here",
              controller: passwordController,
              // prefixIcon: Assets.mailIcon,
            ),
            EntryField(
                controller: confirmPasswordController,
                label: "Confirm New Password",
                hint: "write here"),
            Widgets.heightSpaceH3,
            CustomButton(
              label: "Update Password",
              textColor: ColorConstants.blackColor,
              backgroundColor: ColorConstants.primaryColor,
              radius: 50,
              onTap: () {

                if (oldPasswordController.text.length < 6) {
                  Widgets.showSnackBar("Incomplete Form",
                      "Please enter old password min length 6 characters");
                }
                else if (passwordController.text.length < 6) {
                  Widgets.showSnackBar("Incomplete Form",
                      "Please enter new password min length 6 characters");
                } else if (passwordController.text !=
                    confirmPasswordController.text) {
                  Widgets.showSnackBar(
                      "Incomplete Form", "Passwords are not matching");
                } else {
                  profileController
                      .changePassword(passwordController.text,oldPasswordController.text);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
