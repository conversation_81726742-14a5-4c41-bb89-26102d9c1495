import 'dart:convert';
import 'dart:io';

import 'package:get/get.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/model/booking_detail_model.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/model/bookings_model.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/constants/padding_constants.dart';
import '../../../../../../core/services/http_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import 'package:http/http.dart' as http;

import '../view/leave_submiited_view.dart';

class TravellerBookingController extends GetxController {
  RxString selectedCountry = "".obs;
  RxInt selectedTabIndex = 0.obs;
  RxString selectedTabLabel = "All".obs;
  var selectedBooking=TravellerBooking().obs;
  var bookingDetail=TravellerBookingDetail().obs;
  var endCity = "".obs;
  var startCity = "".obs;
  TextEditingController searchController = TextEditingController();
  RxInt totalBooking = 0.obs; // Rx variable to check if there's more data
  RxInt currentBookingPage = 0.obs; // Rx variable to check if there's more data
  RxBool isBookingMoreLoading = false.obs;
  RxBool isBookingDetailLoading = false.obs;
  RxList userBookings = <TravellerBooking>[].obs;
  RxBool isBookingLoading = false.obs;
  changeTripBookingStatus(String id, String status) async {
    Get.back();
    Widgets.showLoader("Loading..");

    try {
      var response = await ApiService.postData(
        Endpoints.changeBookingStatus,
        {"booking_id": id, "status": status}
      );

      Widgets.hideLoader();
      if (response.status == true) {
        // Get the current tab's status and refresh only that tab
        String currentTabStatus = getStatusFromIndex(selectedTabIndex.value);
        fetchBookings(page: 1, status: currentTabStatus);
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Invalid Code");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }
  changeTripBookingStatusFromDetail(String id, String status) async {
    Get.back();
    Widgets.showLoader("Loading..");

    try {
      var response = await ApiService.postData(
          Endpoints.changeBookingStatus,
          {"booking_id": id, "status": status}
      );

      Widgets.hideLoader();
      if (response.status == true) {
        fetchBookingDetail();
        String currentTabStatus = getStatusFromIndex(selectedTabIndex.value);
        fetchBookings(page: 1, status: currentTabStatus);
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Invalid Code");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }
  String getStatusFromIndex(int index) {
    switch (index) {
      case 0:
        return "all";
      case 1:
        return "upcoming";
      case 2:
        return "in_progress";
      case 3:
        return "completed";
      case 4:
        return "cancelled";
      default:
        return "all";
    }
  }
  fetchBookingDetail() async {

    isBookingDetailLoading.value  = true;
    try {
      var response =
      await ApiService.getData("${Endpoints.fetchBookings}/${selectedBooking.value.id}");
      isBookingDetailLoading.value  = false;

      if (response.status == true) {
    bookingDetail.value=TravellerBookingDetail.fromJson(response.data['booking']);
      } else {
  ;
      }
    } catch (e) {
      isBookingDetailLoading.value  = false;
    } finally {
      isBookingDetailLoading.value  = false;
    }
  }

  fetchBookings({int page = 1, String status = 'all'}) async {
    try {
      if (isBookingLoading.value) return;
      if (page == 1) {
        isBookingLoading.value = true;
      } else {
        isBookingMoreLoading.value = true;
      }
      var response = await ApiService.getData("${Endpoints.fetchBookings}?page=$page&status=$status");

      isBookingLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          userBookings.clear();
          totalBooking.value = 0;
          currentBookingPage.value = 1;
        }

        userBookings.addAll(
          (response.data['bookings'] as List)
              .map((e) => TravellerBooking.fromJson(e))
              .toList(),
        );

        totalBooking.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isBookingLoading.value = false;
      isBookingMoreLoading.value = false;
    } finally {
      isBookingLoading.value = false;
      isBookingMoreLoading.value = false;
    }
  }

  confirmationDialogue(BuildContext context, String bookingId,bool isFromDetail) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            content: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                      margin: EdgeInsets.only(left: 20),
                      alignment: Alignment.topRight,
                      child: InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Icon(CupertinoIcons.clear_circled_solid))),
                  Widgets.heightSpaceH2,
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: ColorConstants.primaryColor,
                    child: Icon(
                      Icons.clear,
                      size: 40,
                    ),
                  ),
                  Widgets.heightSpaceH2,
                  Texts.textBlock(
                    "Cancel Booking",
                    size: 20,
                  ),
                  Widgets.heightSpaceH1,
                  Texts.textMedium("Do you really want to cancel this booking?",
                      size: 14,textAlign: TextAlign.center),
                  Widgets.heightSpaceH3,
                  Padding(
                    padding: PaddingConstants.screenPaddingHalf,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          child: CustomButton(
                            label: "Cancel",
                            borderColor: Colors.transparent,
                            backgroundColor: ColorConstants.silverColor,
                            textColor: ColorConstants.blackColor,
                            radius: 50,
                            onTap: () {
                              Get.back();
                            },
                          ),
                        ),
                        Widgets.widthSpaceW3,
                        Widgets.widthSpaceW1,
                        Expanded(
                          child: CustomButton(
                            label: "Confirm",
                            borderColor: Colors.transparent,
                            backgroundColor: ColorConstants.blackColor,
                            textColor: ColorConstants.whiteColor,
                            radius: 50,
                            onTap: () {
                             isFromDetail?changeTripBookingStatusFromDetail(
                                  bookingId.toString(), "cancelled"): changeTripBookingStatus(
                                  bookingId.toString(), "cancelled");
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  final rating = 0.0.obs;
  final reviewText = ''.obs;
  final selectedImages = <File>[].obs;
  final ImagePicker _picker = ImagePicker();

  Future<void> pickImage() async {
    if (selectedImages.length >= 3) {
      Widgets.showSnackBar('Limit Exceeded', 'You can only upload up to 3 images');
      return;
    }

    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        final directory = await getTemporaryDirectory();
        String newPath = '${directory.path}/compressed_image${Utils.generateUniqueNumber()}.jpg';

        // Compress the image using flutter_image_compress
        var result = await FlutterImageCompress.compressAndGetFile(
          image.path,
          newPath,
          quality: 50, // Set compression quality to 50%
        );

        if (result != null) {
          selectedImages.add(File(result.path));
        }
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  void removeImage(int index) {
    if (index >= 0 && index < selectedImages.length) {
      selectedImages.removeAt(index);
    }
  }

  Future<void> submitReview() async {
    if (rating.value == 0) {
      Widgets.showSnackBar('Required', 'Please rate your experience');
      return;
    }

    try {
      Widgets.showLoader('Submitting review...');

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.saveRatings}'),
      );

      // Add text fields
      request.fields['booking_id'] = selectedBooking.value.id.toString();
      request.fields['rating'] = rating.value.round().toString();
      if (reviewText.value.isNotEmpty) {
        request.fields['review'] = reviewText.value;
      }


      for (int i = 0; i < selectedImages.length; i++) {
        var pic = await http.MultipartFile.fromPath(
          'images[]',
          selectedImages[i].path,
        );
        request.files.add(pic);
      }

      request.headers['Authorization'] = 'Bearer ${Get.find<UserController>().token}';
      var response = await request.send();
      var responseData = await response.stream.bytesToString();
      var jsonResponse = json.decode(responseData);

      Widgets.hideLoader();

      if (jsonResponse['status'] == true) {
        fetchBookingDetail();
        Get.off(() => const ReviewSuccessView());fetchBookings();
      } else {
        Widgets.showSnackBar('Error', jsonResponse['message'] ?? 'Something went wrong');
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar('Error', 'Failed to submit review');
    }finally{
      Widgets.hideLoader();
    }
  }

}
