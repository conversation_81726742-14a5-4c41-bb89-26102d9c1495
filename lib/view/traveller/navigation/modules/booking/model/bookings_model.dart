class TravellerBooking {
  int? id;
  String? title;
  String? startDate;
  String? endDate;
  String? depositAmount;
  String? status;
  int? guideUserId;
  Guide? guide;String? location;
  bool? isReviewDone;
  TravellerBooking(
      {this.id,
        this.title,this.location,
        this.startDate,this.isReviewDone,
        this.endDate,
        this.depositAmount,
        this.status,
        this.guideUserId,
        this.guide});

  TravellerBooking.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    startDate = json['start_date'];
    endDate = json['end_date'];isReviewDone = json['has_rating'];
    depositAmount = json['deposit_amount'].toString();
    status = json['status'];location = json['location'];
    guideUserId = json['guide_user_id'];
    guide = json['guide'] != null ? new Guide.fromJson(json['guide']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['start_date'] = this.startDate;

    data['end_date'] = this.endDate;
    data['deposit_amount'] = this.depositAmount;
    data['status'] = this.status;
    data['guide_user_id'] = this.guideUserId;
    if (this.guide != null) {
      data['guide'] = this.guide!.toJson();
    }
    return data;
  }
}

class Guide {
  int? id;
  String? firstName;
  String? lastName;
  String? image;
  String? imageUrl;
  String? phone;
  String? email;

  Guide({
    this.id,
    this.firstName,
    this.lastName,
    this.image,
    this.imageUrl,
    this.phone,
    this.email
  });

  Guide.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    phone = json['phone'];
    email = json['email'];
    image = json['image'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['phone'] = this.phone;
    data['email'] = this.email;
    data['image'] = this.image;
    data['image_url'] = this.imageUrl;
    return data;
  }
}
