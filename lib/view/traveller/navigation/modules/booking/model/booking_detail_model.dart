import 'bookings_model.dart';

class TravellerBookingDetail {
  int? id;
  int? userId;
  int? guideUserId;
  String? title;
  String? startDate;
  String? endDate;
  String? additionalNotes;
  String? depositAmount;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? location;
  Guide? guide;
  Rating? rating;

  TravellerBookingDetail({
    this.id,
    this.userId,
    this.guideUserId,
    this.location,
    this.title,
    this.startDate,
    this.endDate,
    this.additionalNotes,
    this.depositAmount,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.guide,
    this.rating,
  });

  TravellerBookingDetail.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    guideUserId = json['guide_user_id'];
    title = json['title'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    additionalNotes = json['additional_notes'];
    depositAmount = json['deposit_amount'];
    status = json['status'];
    location = json['location'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    guide = json['guide'] != null ? Guide.fromJson(json['guide']) : null;
    rating = json['rating'] != null ? Rating.fromJson(json['rating']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['guide_user_id'] = this.guideUserId;
    data['title'] = this.title;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['additional_notes'] = this.additionalNotes;
    data['deposit_amount'] = this.depositAmount;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    if (this.guide != null) {
      data['guide'] = this.guide!.toJson();
    }
    if (this.rating != null) {
      data['rating'] = this.rating!.toJson();
    }
    return data;
  }
}

class Rating {
  int? id;
  int? bookingId;
  int? rating;
  String? review;
  List<RatingImage>? images;

  Rating({
    this.id,
    this.bookingId,
    this.rating,
    this.review,
    this.images,
  });

  Rating.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    bookingId = json['booking_id'];
    rating = json['rating'];
    review = json['review'];
    if (json['images'] != null) {
      images = <RatingImage>[];
      json['images'].forEach((v) {
        images!.add(RatingImage.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['booking_id'] = this.bookingId;
    data['rating'] = this.rating;
    data['review'] = this.review;
    if (this.images != null) {
      data['images'] = this.images!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class RatingImage {
  int? ratingId;
  String? image;
  String? imageUrl;

  RatingImage({
    this.ratingId,
    this.image,
    this.imageUrl,
  });

  RatingImage.fromJson(Map<String, dynamic> json) {
    ratingId = json['rating_id'];
    image = json['image'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rating_id'] = this.ratingId;
    data['image'] = this.image;
    data['image_url'] = this.imageUrl;
    return data;
  }
}


