import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../view/traveller_navigation_view.dart';

class ReviewSuccessView extends StatefulWidget {

  const ReviewSuccessView({super.key});

  @override
  State<ReviewSuccessView> createState() => _ReviewSuccessViewState();
}

class _ReviewSuccessViewState extends State<ReviewSuccessView> {



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Center(
              child: Image.asset(
                Assets.success,
                width: .8.sw,
              ),
            ),
            Texts.textBlock("Review Submitted \nSuccessfully!", size: 22,maxline: 2,align:  TextAlign.center),
            Widgets.heightSpaceH2,
            Texts.textNormal("hank you for your feedback! Your review has been submitted successfully and will help other travelers make informed decisions. Keep exploring and enjoy your next adventure!",size: 14),

            Widgets.heightSpaceH4,
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0),
              child: CustomButton(
                label: "Back to bookings",
                borderColor: Colors.transparent,
                backgroundColor: ColorConstants.primaryColor,
                textColor: ColorConstants.blackColor,
                radius: 50,
                onTap: () {
                 Get.back();
                },
              ),
            )

          ],
        ),
      ),
    );
  }
}