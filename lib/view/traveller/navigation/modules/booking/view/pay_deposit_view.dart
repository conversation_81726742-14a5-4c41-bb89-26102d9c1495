import 'dart:io';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/assets_constants.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';

import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/custom_dropdown.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../hosts/controller/home_controller.dart';

class PayDepositView extends StatefulWidget {
  const PayDepositView({super.key});

  @override
  State<PayDepositView> createState() => _PayDepositViewState();
}

class _PayDepositViewState extends State<PayDepositView> {
  late TravellerHomeController homeController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    homeController = Get.find();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: Widgets.customAppBar(title: "Pay A Deposit"),
        body: SingleChildScrollView(
            padding: PaddingConstants.screenPaddingLess,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Widgets.heightSpaceH1,
              Texts.textBlock("Enter your booking information", size: 16),
              Widgets.heightSpaceH2,
              EntryField(textCapitalization: TextCapitalization.words,
                controller: homeController.titleController,
                label: "Title", hint: "write here",

                // prefixIcon: Assets.mailIcon,
              ),
              Obx(
                ()=>Row(
                  children: [
                    Expanded(
                        child: CustomDropdown(
                            onTap: () {
                              homeController.selectDate(context, true);
                            },
                            value: homeController.startDate.value == ""
                                ? null
                                : homeController.startDate.value,
                            hint: "dd/mm/yyyy",
                            label: "Start Date")),
                    const SizedBox(
                      width: 14,
                    ),
                    Expanded(
                        child: CustomDropdown(
                            onTap: () {
                              homeController.selectDate(context, false);
                            },
                            value: homeController.endDate.value == ""
                                ? null
                                : homeController.endDate.value,
                            hint: "dd/mm/yyyy",
                            label: "End Date"))
                  ],
                ),
              ),
              EntryBigField(
                maxLines: null,
                minLines: 7,textCapitalization: TextCapitalization.sentences,
                controller: homeController.descriptionController,
                label: "Additional Notes",
                hint: "Write here",
              ),
              EntryField(
                textInputType:
                    const TextInputType.numberWithOptions(decimal: true),
                label: "Deposit Amount (payable now)", hint: "0",
                controller: homeController.depositAmountController,
prefixIcon: Assets.euroIcon,
                // prefixIcon: Assets.mailIcon,
              ),
              Widgets.heightSpaceH2,
              CustomButton(
                icon: Image.asset(
                  Assets.stripeIcon,
                  width: 40,
                ),
                label: "Deposit Now",
                textColor: ColorConstants.blackColor,
                backgroundColor: Colors.white,
                borderColor: Colors.black87,
                radius: 50,
                onTap: () {
                  homeController.requestForDeposit();
                },
              ),
            ])));
  }
}
