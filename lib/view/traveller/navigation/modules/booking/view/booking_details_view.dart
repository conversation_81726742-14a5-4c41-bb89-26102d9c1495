import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/view/leave_review_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/inbox/model/chat_model.dart';

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/constants/padding_constants.dart';
import '../../../../../../core/routes/app_routes.dart';
import '../../../../../../core/widgets/bookingscreen_card.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../controller/nav_controller.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/view/chat_view.dart';
import '../controller/booking_controller.dart';

class TravellerBookingDetailsView extends StatelessWidget {
  TravellerBookingController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Widgets.customAppBar(title: "Booking Details"),
      body: SingleChildScrollView(
        padding: PaddingConstants.screenPaddingHalf,
        child: Obx(
          () => controller.isBookingDetailLoading.value
              ? buildBookingDetailEffect()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Texts.textBlock("Trip Details", size: 14),
                    Widgets.heightSpaceH2,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Widgets.buildTextGroup(
                              title: "Title",
                              value: controller.bookingDetail.value.title ?? "",
                              icon: Assets.titleIcon),
                        ),
                        Widgets.widthSpaceW4,
                        Expanded(
                          child: Widgets.buildStatusTextGroup(
                              title: "Status",
                              value:  controller.bookingDetail.value.status?.toUpperCase()=="PENDING"?"IN PROGRESS":controller.bookingDetail.value.status?.toUpperCase(),
                              icon: Assets.statusIcon),
                        ),
                      ],
                    ),
                    Widgets.heightSpaceH2,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Widgets.buildTextGroup(
                              title: "Start Date",
                              value: controller.bookingDetail.value.startDate ??
                                  "-",
                              icon: Assets.calendarIcon),
                        ),
                        Widgets.widthSpaceW4,
                        Expanded(
                          child: Widgets.buildTextGroup(
                              title: "End Date",
                              value:
                                  controller.bookingDetail.value.endDate ?? "-",
                              icon: Assets.calendarIcon),
                        ),
                      ],
                    ),
                    Widgets.heightSpaceH2,
                    const Text(
                      "Additional Notes" ?? "",
                      style: TextStyle(
                          fontSize: 10,
                          color: Colors.black54,
                          fontFamily: "InstrumentSansRegular"),
                    ),
                    const SizedBox(height: 2),
                    Texts.textBlock(
                        controller.bookingDetail.value.additionalNotes ?? "-",
                        size: 12,
                        color: ColorConstants.blackColor,
                        maxline: 5,
                        fontWeight: FontWeight.w500),
                    Widgets.heightSpaceH1,
                    Divider(
                      thickness: .5,
                      color: ColorConstants.greyColor,
                    ),
                    Widgets.heightSpaceH1,
                    Texts.textBlock("Host Details", size: 14),
                    Widgets.heightSpaceH2,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Widgets.buildTextGroup(
                              title: "Host Name",
                              value:
                                 "${controller.bookingDetail.value.guide?.firstName}",
                              icon: Assets.profileIcon),
                        ),
                        Widgets.widthSpaceW4,
                        Expanded(
                          child: Widgets.buildTextGroup(
                              title: "Deposit Paid",
                              value:
                                  "${controller.bookingDetail.value.depositAmount ?? "0"}€ ",
                              icon: Assets.dollarIcon),
                        ),
                      ],
                    ),
                    Widgets.heightSpaceH2,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Widgets.buildTextGroup(
                              title: "Email",
                              value:
                                  controller.bookingDetail.value.guide?.email ??
                                      '',
                              icon: Assets.smsIcons),
                        ),
                        Widgets.widthSpaceW4,
                        Expanded(
                          child: Widgets.buildTextGroup(
                              title: "Phone",
                              value:
                                  controller.bookingDetail.value.guide?.phone ??
                                      '----',
                              icon: Assets.phoneIcon),
                        ),
                      ],
                    ),
                    Widgets.heightSpaceH2,
                    Widgets.buildTextGroup(
                        title: "Location",
                        value: controller.bookingDetail.value.location ?? "-",
                        icon: Assets.locationIcon),

                    Widgets.heightSpaceH3,

                    if (controller.bookingDetail.value.rating != null) ...[
                      Divider(
                        thickness: .5,
                        color: ColorConstants.greyColor,
                      ),
                      Widgets.heightSpaceH1,
                      Texts.textBlock("Your Review", size: 14),
                      Widgets.heightSpaceH1,
                      Widgets.buildRatingStar(
                        controller.bookingDetail.value.rating?.rating?.toDouble() ?? 0,
                      ),
                      if (controller.bookingDetail.value.rating?.review != null) ...[
                        Widgets.heightSpaceH1,
                        Texts.textMedium(
                          controller.bookingDetail.value.rating?.review ?? "",
                          size: 12,
                          color: ColorConstants.textColor,
                        ),
                      ],
                      if ((controller.bookingDetail.value.rating?.images?.isNotEmpty ?? false)) ...[
                        Widgets.heightSpaceH2,
                        SizedBox(
                          height: 100,
                          child: ListView.separated(
                            scrollDirection: Axis.horizontal,
                            itemCount: controller.bookingDetail.value.rating?.images?.length ?? 0,
                            separatorBuilder: (context, index) => SizedBox(width: 8),
                            itemBuilder: (context, index) {
                              return ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Widgets.networkImage(
                                  controller.bookingDetail.value.rating?.images?[index].imageUrl ?? "",
                                  height: 100,
                                  width: 100,

                                ),
                              );
                            },
                          ),
                        ),
                      ],
                      Widgets.heightSpaceH3,
                    ],

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: CustomButton(
                            onTap: () {
                              final existingChat =
                              Get.find<ChatController>()
                                  .chats
                                  .firstWhereOrNull((chat) =>
                              chat.otherUser?.id ==
                                  controller.bookingDetail.value.guide?.id);
                              existingChat == null
                                  ? Get.to(() => ChatView(
                                chatUser: OtherUser(
                                  id: controller.bookingDetail.value.guide?.id,
                                  name: "${controller.bookingDetail.value.guide?.firstName}",
                                  image: controller.bookingDetail.value.guide?.imageUrl,
                                ),
                              ))?.then((value) {

                              })
                                  : Get.to(() => ChatView(
                                chatId:
                                existingChat.chatId.toString(),
                                chatUser: OtherUser(
                                  id: controller.bookingDetail.value.guide?.id,
                                  name: "${controller.bookingDetail.value.guide?.firstName}",
                                  image: controller.bookingDetail.value.guide?.imageUrl,
                                ),
                              ))?.then((value) {


                              });
                            },
                            label: "Chat with Host",
                            textColor: ColorConstants.blackColor,
                            fontSize: 12,
                            backgroundColor: ColorConstants.primaryColor,
                            radius: 50,
                          ),
                        ),
                        Widgets.widthSpaceW3,
                        if (controller.bookingDetail.value.status?.toLowerCase() == "completed" &&
                            controller.bookingDetail.value.rating == null)
                          Expanded(
                            child: CustomButton(
                              label: "Leave Review",
                              onTap: () {
                                controller.selectedImages.clear();
                                controller.reviewText.value = "";
                                controller.rating.value = 0;
                                Get.to(() => TravellerLeaveReview());
                              },
                              textColor: ColorConstants.whiteColor,
                              radius: 50,
                              fontSize: 12,
                              backgroundColor: ColorConstants.splash,
                            ),
                          )
                        else if (controller.bookingDetail.value.status?.toLowerCase() == "pending")
                          Expanded(
                            child: CustomButton(
                              onTap: () {
                                controller.confirmationDialogue(
                                  context,
                                  controller.bookingDetail.value.id.toString(),
                                  true,
                                );
                              },
                              label: "Cancel Booking",
                              textColor: ColorConstants.whiteColor,
                              radius: 50,
                              fontSize: 12,
                              backgroundColor: ColorConstants.redColor,
                            ),
                          ),
                      ],
                    ),
                    Widgets.heightSpaceH1,
                  ],
                ),
        ),
      ),
    );
  }
}
