import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/view/payment_success_view.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PaymentProcessView extends StatefulWidget {
  final String url;

  PaymentProcessView({required this.url});

  @override
  _PaymentProcessViewState createState() => _PaymentProcessViewState();
}

class _PaymentProcessViewState extends State<PaymentProcessView> {
  late final WebViewController _controller;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();

    final WebViewController controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) {

            if (request.url.contains('cancel')) {
              Get.back();
              return NavigationDecision.prevent;
            } else if (request.url.contains("complete")) {
              Get.off(PaymentSuccesView(url: request.url,));
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));

    _controller = controller;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            WebViewWidget(controller: _controller),
            if (isLoading)
              Container(
                color: Colors.white,
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    CircularProgressIndicator(),
                    SizedBox(height: 30.0),
                    Text("Redirecting to payments page"),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
