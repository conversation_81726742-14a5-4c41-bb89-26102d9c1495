import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/widgets/text_widgets.dart';

import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/constants/padding_constants.dart';

import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../controller/booking_controller.dart';

class TravellerLeaveReview extends StatelessWidget {
  TravellerBookingController controller = Get.find();

  @override
  Widget build(BuildContext context) {


    return Scaffold(
      appBar: Widgets.customAppBar(title: "Leave A Review"),
      body: SingleChildScrollView(
        child: Padding(
          padding: PaddingConstants.screenPaddingHalf,
          child: Obx(
           () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textBlock("Review Your Host", size: 14),
                Widgets.heightSpaceH05,
                Texts.textMedium(
                    "Share your experience and help others choose the best hosts!",
                    color: ColorConstants.textColor,
                    size: 11),
                Widgets.heightSpaceH3,
                Texts.textBlock("Host Information", size: 14),
                Widgets.heightSpaceH1,
                Row(
                  children: [
                   ClipRRect(borderRadius: BorderRadius.circular(10),
                     child: Widgets.networkImage(controller.bookingDetail.value.guide?.imageUrl ?? "",
                       height: 60,
                       width: 60,
                     ),),
                    const SizedBox(
                      width: 5,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBlock("${controller.bookingDetail.value.guide?.firstName}",
                            size: 14,
                            color: ColorConstants.blackColor,
                            fontWeight: FontWeight.w600),
                        const SizedBox(height: 3),
                        Text(
                          controller.bookingDetail.value.location ?? "",
                          style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black,
                              fontFamily: "InstrumentSansRegular"),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          "Booking Data: ${controller.bookingDetail.value.startDate}",
                          style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black,
                              fontFamily: "InstrumentSansRegular"),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          "Paid Amount: ${controller.bookingDetail.value.depositAmount}€",
                          style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black,
                              fontFamily: "InstrumentSansRegular"),
                        ),
                      ],
                    ),
                    const SizedBox(
                      width: 15,
                    ),

                  ],
                ),
                Widgets.heightSpaceH2,
                Texts.textBlock("Rate Your Experience", size: 14),
                Widgets.heightSpaceH2,
                RatingBar.builder(
                  updateOnDrag: true,
                  initialRating: controller.rating.value,
                  itemSize: 25,
                  glow: false,
                  unratedColor: ColorConstants.greyColor,
                  minRating: 1,
                  direction: Axis.horizontal,
                  allowHalfRating: true,
                  itemCount: 5,
                  itemPadding: const EdgeInsets.symmetric(horizontal: 3.0),
                  itemBuilder: (context, i) => Icon(
                    CupertinoIcons.star_fill,
                    color: ColorConstants.splash,
                  ),
                  onRatingUpdate: (rating) {
                    controller.rating.value = rating;
                  },
                ),
                Widgets.heightSpaceH2,
                EntryBigField(textCapitalization: TextCapitalization.sentences,
                  maxLines: null,
                  minLines: 7,
                  hint: "Write a review (Optional)",
                  onChange: (value) {
                    controller.reviewText.value = value!;
                  },
                ),
                Widgets.heightSpaceH2,
                Row(
                  children: [
                    Texts.textBlock("Upload Image", size: 14),
                    Texts.textMedium(" (Optional - Max 3)", size: 11)
                  ],
                ),
                Widgets.heightSpaceH05,
                Texts.textMedium(
                  "Upload pictures from your trip!",
                  color: ColorConstants.textColor,
                  size: 11
                ),
                Widgets.heightSpaceH2,
                Obx(() => Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    ...controller.selectedImages.asMap().entries.map((entry) {
                      return Stack(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              image: DecorationImage(
                                image: FileImage(entry.value),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          Positioned(
                            right: -8,
                            top: -8,
                            child: IconButton(
                              icon: const Icon(Icons.cancel, color: Colors.red),
                              onPressed: () => controller.removeImage(entry.key),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                    if (controller.selectedImages.length < 3)
                      GestureDetector(
                        onTap: controller.pickImage,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: ColorConstants.lightOrange,
                            border: Border.all(
                              color: ColorConstants.splash,
                              width: .5
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                CupertinoIcons.cloud_upload,
                                color: ColorConstants.splash
                              ),
                              Texts.textMedium(
                                "Upload",
                                size: 12,
                                color: ColorConstants.splash
                              )
                            ],
                          ),
                        ),
                      ),
                  ],
                )),
                Widgets.heightSpaceH3,
                CustomButton(
                  label: "Submit Review",
                  borderColor: Colors.transparent,
                  backgroundColor: ColorConstants.primaryColor,
                  textColor: ColorConstants.blackColor,
                  radius: 50,
                  onTap: () => controller.submitReview(),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
