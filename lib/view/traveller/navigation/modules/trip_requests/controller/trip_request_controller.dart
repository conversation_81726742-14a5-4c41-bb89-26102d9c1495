import 'dart:async';
import 'dart:convert';

import 'package:country_picker/country_picker.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:indyguide/view/traveller/navigation/controller/nav_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/model/user_trip_request_model.dart';
import 'package:intl/intl.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/services/http_service.dart';
import '../../../../../../core/utils/utils.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../model/city_model.dart';

class TravellerTripRequestController extends GetxController {
 RxInt selectedTabIndex = 0.obs;
  RxString selectedTabLabel = "All".obs;
  RxList<String> selectedInterests = <String>[].obs;
  RxList<String> selectedItems = <String>[].obs;
  RxList<String> selectedCountries = <String>[].obs;

  var endCity= "".obs;
  var startCity= "".obs;
  TextEditingController searchController = TextEditingController();


 RxString startDate = "".obs;
  RxString endDate = "".obs;
  RxDouble minBudget = 0.0.obs;
  RxDouble maxBudget = 0.0.obs;
 var selectedTripRequest=UserTripRequest().obs;

  RxInt totalRequests = 0.obs; // Rx variable to check if there's more data
  RxInt currentRequestPage = 0.obs; // Rx variable to check if there's more data
  RxBool isTripRequestMoreLoading = false.obs;

  RxList userTripRequests = <UserTripRequest>[].obs;
  RxBool isTripRequestLoading = false.obs;
  TextEditingController descriptionController = TextEditingController();
 TextEditingController numberOfPeople = TextEditingController();



  changeTripRequestStatus(int id) async {
    Widgets.showLoader("Loading..");

    try {
      var response =
          await ApiService.getData("${Endpoints.userChangeTripRequest}/$id");

      Widgets.hideLoader();
      if (response.status == true) {
        fetchRequests(page: 1);
      } else {
        Widgets.showSnackBar("Error", response.message ?? "Invalid Code");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }

  fetchRequests({int page = 1}) async {
    try {
      if (isTripRequestLoading.value) return;
      if (page == 1) {
        isTripRequestLoading.value = true;
      } else {
        isTripRequestMoreLoading.value = true;
      }
      var response = await ApiService.getData(Endpoints.fetchUserTripRequests+"?page=$page");

      isTripRequestLoading.value = false;
      isTripRequestMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          userTripRequests.clear();
          totalRequests.value = 0;
          currentRequestPage.value = 1;
        }

        userTripRequests.addAll(
          (response.data['trip_requests'] as List)
              .map((e) => UserTripRequest.fromJson(e))
              .toList(),
        );

        totalRequests.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isTripRequestLoading.value = false;
      isTripRequestMoreLoading.value = false;
    } finally {
      isTripRequestLoading.value = false;
      isTripRequestMoreLoading.value = false;
    }
  }

  void toggleInterest(String interest) {
    if (selectedInterests.contains(interest)) {
      selectedInterests.remove(interest);
    } else {
      selectedInterests.add(interest);
    }
  }

  Future<void> selectDate(BuildContext context, bool isStartDate) async {
    DateTime firstDate = isStartDate
        ? DateTime.now()
        : (startDate.value.isNotEmpty
            ? DateFormat('dd/MM/yyyy').parse(startDate.value)
            : DateTime.now());
    DateTime lastDate = isStartDate
        ? (endDate.value.isNotEmpty
            ? DateFormat('dd/MM/yyyy').parse(endDate.value)
            : DateTime(2100))
        : DateTime(2100);

    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: firstDate,
      firstDate: firstDate,
      lastDate: lastDate, builder: (context, child) {
      return Theme(
        data: Theme.of(context).copyWith(
          colorScheme: ColorScheme.light(
            primary: ColorConstants.primaryColor,
          ),
        ),
        child: child!,
      );
    },
    );

    if (picked != null) {
      String formattedDate = Utils.formatDate(picked);

      if (isStartDate) {
        startDate.value = formattedDate;
        if (endDate.value.isNotEmpty &&
            DateFormat('dd/MM/yyyy').parse(endDate.value).isBefore(picked)) {
          endDate.value =
              formattedDate; // Adjust endDate if it's before startDate
        }
      } else {
        if (startDate.value.isNotEmpty &&
            picked.isBefore(DateFormat('dd/MM/yyyy').parse(startDate.value))) {
          Widgets.showSnackBar(
            "Invalid Date",
            "End date cannot be earlier than start date.",
          );
        } else {
          endDate.value = formattedDate;
        }
      }
    }
  }





  void showMultiSelectBottomSheet(BuildContext context, String title,
      List<String> items, RxList<String> selectedItems) {
    final searchController = TextEditingController();
    final RxList<String> filteredItems = RxList<String>(items);

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true, // Add this to make bottom sheet larger
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Container(
          padding: const EdgeInsets.all(20),
          // Set height to 90% of screen height
          height: MediaQuery.of(context).size.height * 0.9,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock(
                    "Select $title",
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const SizedBox(height: 10),

              // Search Field
              EntrySearchField(
                controller: searchController,
                prefixIcon: Assets.searchIcon,
                hint: "Search $title",
                onChange: (value) {
                  if (value != null) {
                    filteredItems.value = items
                        .where((item) =>
                        item.toLowerCase().contains(value.toLowerCase()))
                        .toList();
                  } else {
                    filteredItems.value = items;
                  }
                },
              ),

              const SizedBox(height: 10),

              Expanded(
                child: Obx(() => ListView.separated(
                  itemCount: filteredItems.length,
                  itemBuilder: (context, index) {
                    final item = filteredItems[index];
                    return Obx(() => CheckboxListTile(
                      contentPadding: EdgeInsets.zero,
                      title: Text(item),
                      value: selectedItems.contains(item),
                      onChanged: (bool? value) {
                        if (value == true) {
                          selectedItems.add(item);
                        } else {
                          selectedItems.remove(item);
                        }
                      },
                    ));
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return Divider(
                        color: ColorConstants.greyColor, thickness: .5);
                  },
                )),
              ),

              CustomButton(
                label: "Done",
                textColor: ColorConstants.blackColor,
                backgroundColor: ColorConstants.primaryColor,
                radius: 50,
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
      ),
    );
  }



  createTripRequestAPi() async {
    try {
      if (selectedCountries.isEmpty) {
        return Widgets.showSnackBar(
            "Incomplete Form", "Please select country.");
      } else if (startCity.value == "") {
        return Widgets.showSnackBar(
            "Incomplete Form", "Please select start city.");
      }  else if (endCity.value == "") {
        return Widgets.showSnackBar(
            "Incomplete Form", "Please select end city.");
      }else if (startDate.value == "") {
        return Widgets.showSnackBar(
            "Incomplete Form", "Please select start date.");
      } else if (endDate.value == "") {
        return Widgets.showSnackBar(
            "Incomplete Form", "Please select end date.");
      } else if (descriptionController.text.isEmpty) {
        return Widgets.showSnackBar(
            "Incomplete Form", "Please enter something about your trip");
      }else if (numberOfPeople.text.isEmpty) {
        return Widgets.showSnackBar(
            "Incomplete Form", "Please enter number of people");
      }
      Widgets.showLoader("Loading.. ");
      var data = {
        "country": selectedCountries.toList(),
        "start_city":startCity.value,
        "end_city": endCity.value,
        "start_date": startDate.value,"no_of_attendees":numberOfPeople.text,
        "end_date": endDate.value,
        "description": descriptionController.text,
        "budget_min": minBudget.value.toString(),
        "budget_max": maxBudget.value.toString(),
        "interests": selectedInterests.join(', ')
      };

      var response =
          await ApiService.postData(Endpoints.createTripRequest, data);
      Widgets.hideLoader();

      if (response.status == true) {Get.back();
        Widgets.showSnackBar(
            "Success", "Trip request has been created successfully.");
        fetchRequests(page: 1);Get.find<TravellerNavController>().changeIndex(0);
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
    } finally {
      Widgets.hideLoader();
    }
  }
 editTripRequestAPi() async {
   try {
     if (selectedCountries.isEmpty) {
       return Widgets.showSnackBar(
           "Incomplete Form", "Please select country.");
     } else if (startCity.value == "") {
       return Widgets.showSnackBar(
           "Incomplete Form", "Please select start city.");
     }  else if (endCity.value == "") {
       return Widgets.showSnackBar(
           "Incomplete Form", "Please select end city.");
     }else if (startDate.value == "") {
       return Widgets.showSnackBar(
           "Incomplete Form", "Please select start date.");
     } else if (endDate.value == "") {
       return Widgets.showSnackBar(
           "Incomplete Form", "Please select end date.");
     } else if (descriptionController.text.isEmpty) {
       return Widgets.showSnackBar(
           "Incomplete Form", "Please enter something about your trip");
     }else if (numberOfPeople.text.isEmpty) {
       return Widgets.showSnackBar(
           "Incomplete Form", "Please enter number of people");
     }
     Widgets.showLoader("Loading.. ");
     var data = {
       "country": selectedCountries.toList(),
       "start_city":startCity.value,
       "end_city": endCity.value,"no_of_attendees":numberOfPeople.text,
       "start_date": startDate.value,
       "end_date": endDate.value, "trip_request_id": selectedTripRequest.value.id,
       "description": descriptionController.text,
       "budget_min": minBudget.value.toString(),
       "budget_max": maxBudget.value.toString(),
       "interests": selectedInterests.join(', ')
     };

     var response =
     await ApiService.postData(Endpoints.createTripRequest, data);
     Widgets.hideLoader();

     if (response.status == true) {Get.back();
     Widgets.showSnackBar(
         "Success", "Trip request has been created successfully.");
     fetchRequests(page: 1);
     } else {
       Widgets.showSnackBar("Error", response.message ?? "");
     }
   } catch (e) {
   } finally {
     Widgets.hideLoader();
   }
 }

  void updateBudget(double start, double end) {
    minBudget.value = start;
    maxBudget.value = end;
  }

  resetAllFields() {
    selectedCountries.clear();
    startDate.value = "";
    endDate.value = "";
    startCity.value = "";
    endCity.value = "";
    descriptionController.clear();
    minBudget.value = 0.0;
    maxBudget.value = 0.0;
    selectedInterests.clear();
  }

}

RxInt currentNotificationsPage = 1.obs;
