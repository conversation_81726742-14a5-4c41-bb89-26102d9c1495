import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/color_constants.dart';

import 'package:indyguide/core/widgets/widgets.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/controller/trip_request_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/model/user_trip_request_model.dart';

import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import 'create_request_view.dart';
import 'edit_trip_request_view.dart';

class TravellerTripRequestsView extends StatefulWidget {
  TravellerTripRequestsView({super.key});

  @override
  State<TravellerTripRequestsView> createState() =>
      _TravellerTripRequestsViewState();
}

class _TravellerTripRequestsViewState extends State<TravellerTripRequestsView>
    with SingleTickerProviderStateMixin {
  late TabController tabController;
  late TravellerTripRequestController tripController;
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    tripController = Get.find();
    tabController = TabController(length: 3, vsync: this);
    tripController.selectedTabIndex.value = 0;
    tripController.selectedTabLabel.value = "All";
    tripController.isTripRequestLoading.value = false;
    tripController.isTripRequestMoreLoading.value = false;
    tripController.fetchRequests(page: 1);
    scrollController.addListener(scrollListener);
  }

  @override
  void dispose() {
    tabController.dispose();

    super.dispose();
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        tripController.totalRequests.value >
            tripController.userTripRequests.length) {
      tripController.fetchRequests(
          page: tripController.currentRequestPage.value + 1);
      tripController.currentRequestPage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
          shape: const CircleBorder(),
          backgroundColor: ColorConstants.primaryColor,
          child: const Icon(Icons.add),
          onPressed: () {
            tripController.resetAllFields();
            Get.to(() => const CreateTripRequestView());
          }),
      appBar: AppBar(
        backgroundColor: Colors.white,
        automaticallyImplyLeading: false,
        title: Texts.textMedium("Trip Requests",
            size: 20, fontWeight: FontWeight.w600),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(left: 15),
            height: 35,
            child: ListView(
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              children: [
                buildTab("All", 0),
                Widgets.widthSpaceW2,
                buildTab("Active", 1),
                Widgets.widthSpaceW2,
                buildTab("Deactivated", 2),
              ],
            ),
          ),
          Widgets.heightSpaceH1,
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TabBarView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: tabController,
                  children: [
                    buildAllRequestsList(),
                    buildActiveRequestsList(),
                    buildInActiveRequestsList(),
                  ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildTab(String label, int index) {
    return Obx(() {
      final isSelected = tripController.selectedTabIndex.value == index;
      return GestureDetector(
        onTap: () {
          tripController.selectedTabIndex.value = index;
          tripController.selectedTabLabel.value = label;
          tabController.animateTo(index);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 15,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? ColorConstants.blackColor
                : ColorConstants.silverColor,
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Center(
            child: Texts.textMedium(label,
                color: isSelected ? Colors.white : Colors.black, size: 12),
          ),
        ),
      );
    });
  }

  Widget buildAllRequestsList() {
    return Obx(
      () {
        return ListView(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          children: [
            Widgets.heightSpaceH2,
            tripController.isTripRequestLoading.value
                ? const ShimmerListSkeleton()
                : tripController.userTripRequests.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          UserTripRequest tripRequest =
                              tripController.userTripRequests[index];
                          return Widgets.buildRequestCard(
                            request: tripRequest,
                            onTap: () {
                              tripController
                                  .changeTripRequestStatus(tripRequest.id ?? 0);
                            },onEditTap: () {

                              tripController.selectedTripRequest.value=tripRequest;
                              Get.to(() => const EditTripRequestView()); },
                          );
                        },
                        separatorBuilder: (context, index) {
                          return Widgets.heightSpaceH1;
                        },
                        itemCount: tripController.userTripRequests.length ?? 0)
                    : Widgets.noRecordsFound(title: "No Requests so far"),
            if (tripController.isTripRequestMoreLoading.value)
              Widgets.moreLoading(),
          ],
        );
      },
    );
  }

  Widget buildInActiveRequestsList() {
    return Obx(
      () {
        return ListView(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          children: [
            Widgets.heightSpaceH2,
            tripController.isTripRequestLoading.value
                ? const ShimmerListSkeleton()
                : tripController.userTripRequests.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          UserTripRequest tripRequest =
                              tripController.userTripRequests[index];
                          return tripRequest.status == 0
                              ? Widgets.buildRequestCard(
                                  request: tripRequest,
                                  onTap: () {
                                    tripController.changeTripRequestStatus(
                                        tripRequest.id ?? 0);
                                  }, onEditTap: () {
                            tripController.selectedTripRequest.value=tripRequest;
                                    Get.to(() => const EditTripRequestView()); },
                                )
                              : const SizedBox.shrink();
                        },
                        separatorBuilder: (context, index) {
                          return Widgets.heightSpaceH1;
                        },
                        itemCount: tripController.userTripRequests.length ?? 0)
                    : Widgets.noRecordsFound(title: "No Requests so far"),
            if (tripController.isTripRequestMoreLoading.value)
              Widgets.moreLoading(),
          ],
        );
      },
    );
  }

  Widget buildActiveRequestsList() {
    return Obx(
      () {
        return ListView(
          controller: scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          children: [
            Widgets.heightSpaceH2,
            tripController.isTripRequestLoading.value
                ? const ShimmerListSkeleton()
                : tripController.userTripRequests.isNotEmpty
                    ? ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          UserTripRequest tripRequest =
                              tripController.userTripRequests[index];
                          return tripRequest.status == 1
                              ? Widgets.buildRequestCard(
                                  request: tripRequest,
                                  onTap: () {
                                    tripController.changeTripRequestStatus(
                                        tripRequest.id ?? 0);
                                  },onEditTap: () {

                            tripController.selectedTripRequest.value=tripRequest;
                                    Get.to(() => const EditTripRequestView()); },
                                )
                              : const SizedBox.shrink();
                        },
                        separatorBuilder: (context, index) {
                          return Widgets.heightSpaceH1;
                        },
                        itemCount: tripController.userTripRequests.length ?? 0)
                    : Widgets.noRecordsFound(title: "No Requests so far"),
            if (tripController.isTripRequestMoreLoading.value)
              Widgets.moreLoading(),
          ],
        );
      },
    );
  }
}
