import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/widgets/custom_dropdown.dart';
import 'package:indyguide/core/widgets/entry_field.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/controller/trip_request_controller.dart';
import 'package:indyguide/core/widgets/select_city_view.dart';

import '../../../../../../core/constants/constants_list.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';

class CreateTripRequestView extends StatefulWidget {
  const CreateTripRequestView({super.key});

  @override
  State<CreateTripRequestView> createState() => _CreateTripRequestViewState();
}

class _CreateTripRequestViewState extends State<CreateTripRequestView> {
  final TravellerTripRequestController tripController = Get.find();

  @override
  Widget build(BuildContext context) {
    return SafeArea(bottom: true,top: false,
      child: Scaffold(

        appBar: Widgets.customAppBar(title: "Tell Us About Your Trip"),
        body: SingleChildScrollView(
          padding: PaddingConstants.screenPaddingLess,
          child: Obx(
            () => Column(crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                EntryBigField(textCapitalization: TextCapitalization.sentences,
                  maxLines: null,
                  minLines: 7,
                  controller: tripController.descriptionController,
                  label: "Tell us about your trip",
                  hint: "Write here",
                ),


                CustomDropdown2(
                  onTap: () => tripController.showMultiSelectBottomSheet(
                    context,
                    "Countries",
                    Data.countries,
                    tripController.selectedCountries,
                  ),
                  value:   tripController.selectedCountries.isEmpty
                      ? null
                      :   tripController.selectedCountries.join(", "),
                  hint: "Select countries",
                  label: "Country",
                ),

                Row(
                  children: [
                    Expanded(
                        child: CustomDropdown(
                            onTap: () {
                              if (tripController.selectedCountries.isEmpty) {
                                Widgets.showSnackBar(
                                    "Incomplete Form", "Please select country.");
                              } else {
                                Get.to(()=>SelectCityListView(
                                  countries: tripController.selectedCountries,
                                ))?.then((result) {
                                  if (result != null) {
                                    print(result);
                                    tripController.startCity.value = result['city'];
                                    // You can also use result['country'] if needed
                                  }
                                });
                              }
                            },
                            value: tripController.startCity.value != ""
                                ? tripController.startCity.value
                                : null,
                            hint: "--Select--",
                            label: "Start City")),
                    const SizedBox(
                      width: 14,
                    ),
                    Expanded(
                        child: CustomDropdown(
                            onTap: () {
                                if (tripController.selectedCountries.isEmpty) {
                                Widgets.showSnackBar(
                                    "Incomplete Form", "Please select country.");
                              } else {
                                Get.to(()=>SelectCityListView(
                                  countries: tripController.selectedCountries,
                                ))?.then((result) {
                                  if (result != null) {
                                    print(result);
                                    tripController.endCity.value = result['city'];
                                    // You can also use result['country'] if needed
                                  }
                                });
                              }
                            },
                            value: tripController.endCity.value != ""
                                ? tripController.endCity.value
                                : null,
                            hint: "--Select--",
                            label: "End City"))
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                        child: CustomDropdown(
                            onTap: () {
                              tripController.selectDate(context, true);
                            },
                            value: tripController.startDate.value == ""
                                ? null
                                : tripController.startDate.value,
                            hint: "dd/mm/yyyy",
                            label: "Start Date")),
                    const SizedBox(
                      width: 14,
                    ),
                    Expanded(
                        child: CustomDropdown(
                            onTap: () {
                              tripController.selectDate(context, false);
                            },
                            value: tripController.endDate.value == ""
                                ? null
                                : tripController.endDate.value,
                            hint: "dd/mm/yyyy",
                            label: "End Date"))
                  ],
                ), EntryField(

                  controller: tripController.numberOfPeople,
                  label: "Number of People",
                  hint: "Write here",textInputType: TextInputType.number,
                ),

                Widgets.heightSpaceH1,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Texts.textBlock(
                      fontWeight: FontWeight.w600,
                      "Services",
                      size: 10,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 7,
                ),
                Obx(() => Wrap(
                      spacing: 8,
                      children: Data.allInterests.map((interest) {
                        bool isSelected =
                            tripController.selectedInterests.contains(interest);

                        return ChoiceChip(
                          label: Text(interest,
                              style: TextStyle(
                                  fontFamily: "InstrumentSansRegular",
                                  fontSize: 9,
                                  color: isSelected
                                      ? ColorConstants.splash
                                      : Colors.black54,
                                  fontWeight: FontWeight.w800)),
                          selected:
                              tripController.selectedInterests.contains(interest),
                          onSelected: (selected) {
                            tripController.toggleInterest(interest);
                          },
                          showCheckmark: false,
                          selectedColor: ColorConstants.lightOrange,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(20), // Rounded edges
                            side: isSelected
                                ? BorderSide(
                                    color: ColorConstants.primaryColor,
                                    width: 1) // Highlight selected
                                : const BorderSide(color: Colors.transparent),
                          ),
                          backgroundColor: ColorConstants.greyColor,
                        );
                      }).toList(),
                    )),



                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 00.0, vertical: 30),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        child: CustomButton(
                          label: "Cancel",
                          fontSize: 12,
                          borderColor: ColorConstants.blackColor,
                          textColor: ColorConstants.blackColor,
                          radius: 50,
                          onTap: () {
                            Get.back();
                          },
                        ),
                      ),
                      Widgets.widthSpaceW3,
                      Expanded(
                        child: CustomButton(
                          label: "Submit Request",
                          fontSize: 12,
                          borderColor: Colors.transparent,
                          backgroundColor: ColorConstants.primaryColor,
                          textColor: ColorConstants.blackColor,
                          radius: 50,
                          onTap: () {
                            tripController.createTripRequestAPi();
                          },
                        ),
                      ),
                    ],
                  ),
                ),Widgets.heightSpaceH5,Widgets.heightSpaceH5,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildBudgetInput(RxDouble value) {
    return Expanded(
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstants.greyColor),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Obx(() => Text(
                    "${value.value.toInt()}",
                    style: const TextStyle(fontSize: 13, color: Colors.black45),
                  )),
            ),
            Texts.textNormal("€", color: Colors.black54, size: 14),
          ],
        ),
      ),
    );
  }
}
