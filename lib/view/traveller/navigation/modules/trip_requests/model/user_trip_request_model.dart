class UserTripRequest {
  int? id;
  int? userId;
  List<String>? country;
  String? startCity;
  String? endCity;
  String? startDate;
  String? endDate;
  String? description;
  String? budgetMin;
  String? budgetMax;
  String? interests;
  int? status; int? numberOfAttendees;
  String? createdAt;
  String? updatedAt;

  UserTripRequest({
    this.id,
    this.userId,
    this.country,
    this.startCity,
    this.endCity,
    this.startDate,
    this.endDate,
    this.description,this.numberOfAttendees,
    this.budgetMin,
    this.budgetMax,
    this.interests,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  UserTripRequest.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];

      country = json['country'].cast<String>();
     numberOfAttendees = json['no_of_attendees']??0;
    startCity = json['start_city'];
    endCity = json['end_city'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    description = json['description'];
    budgetMin = json['budget_min'];
    budgetMax = json['budget_max'];
    interests = json['interests'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['country'] = this.country;
    data['start_city'] = this.startCity;
    data['end_city'] = this.endCity;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['description'] = this.description;
    data['budget_min'] = this.budgetMin;
    data['budget_max'] = this.budgetMax;
    data['interests'] = this.interests;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
