import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:indyguide/view/traveller/navigation/modules/booking/view/bookings_view.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/controller/trip_request_controller.dart';
import 'package:indyguide/view/traveller/navigation/modules/trip_requests/view/trip_requests_view.dart';
import '../modules/booking/controller/booking_controller.dart';
import '../modules/hosts/controller/home_controller.dart';
import '../modules/hosts/view/hosts_view.dart';
import '../modules/inbox/controller/chat_controller.dart';
import '../modules/inbox/view/inbox_view.dart';
import '../modules/settings/view/settings_view.dart';
import '../../../../core/services/socket_service.dart';

class TravellerNavController extends GetxController {
  RxInt currentIndex = 0.obs;
  late TravellerHomeController homeController;
  late ChatController chatController;

  late TravellerTripRequestController tripRequestController;
  late TravellerBookingController bookingViewController;
  @override
  void onInit() {
    super.onInit();
    homeController = Get.put(TravellerHomeController());
    chatController = Get.put(ChatController());
    tripRequestController = Get.put(TravellerTripRequestController());
    bookingViewController = Get.put(TravellerBookingController());
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: DateTime.now().millisecondsSinceEpoch ~/
              1000, // Unique ID for each notification
          channelKey: 'default_channel_id',
          title: message.notification?.title ?? 'Default Title',
          body: message.notification?.body ?? 'Default Body',
          notificationLayout: NotificationLayout.Default,
        ),
      );
    });
  }



  final List screens = [
    const TravellerHomeView(),
    TravellerInboxView(),
    TravellerTripRequestsView(),
    TravellerBookingsView(),
    TravellerSettingsView(),
  ];

  void changeIndex(int index) {
    currentIndex.value = index;
  }
}
