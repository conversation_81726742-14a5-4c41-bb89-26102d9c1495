import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:indyguide/controller/user_controller.dart';
import '../../core/constants/assets_constants.dart';
import '../../core/constants/color_constants.dart';
import '../../core/routes/app_routes.dart';
import 'onboarding_view.dart';

class SplashView extends StatefulWidget {
  const SplashView({super.key});

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {
  late UserController userController;
  @override
  void initState() {
    super.initState();
    userController=Get.put(UserController());
    //
     userController.navigateToNextScreen();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorConstants.primaryColor,
        body: Center(child:  Image.asset(
          Assets.appLogo,
          width: .70.sw,
        ),));
  }
}
