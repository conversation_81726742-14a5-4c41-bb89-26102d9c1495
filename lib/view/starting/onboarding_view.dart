import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:indyguide/core/constants/color_constants.dart';
import 'package:indyguide/core/constants/padding_constants.dart';
import 'package:indyguide/core/routes/app_routes.dart';
import 'package:indyguide/core/widgets/custom_button.dart';
import '../../controller/onboarding_controller.dart';
import '../../core/widgets/onbarding_page.dart';
import '../../core/widgets/widgets.dart';

class BoardingView extends StatelessWidget {
  OnboardingController onboardingController = Get.put(OnboardingController());
  final PageController controller = PageController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
              alignment: Alignment.bottomCenter,
              children: [
        PageView.builder(
            itemCount: onboardingController.sliderData.length,
            controller: controller,
            onPageChanged: onboardingController.onPageChanged,
            itemBuilder: (context, index) {
              return OnBoardingPage(onboardingController.sliderData[index]);
            }),
        Padding(
          padding: const EdgeInsets.only(bottom: 130.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              for (int i = 1; i <= 3; i++)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Obx(
                    () => onboardingController.ProperCircleIndexWidget(i),
                  ),
                ),
            ],
          ),
        ),
        Obx(
          () => onboardingController.currentPage.value < 3
              ? Padding(
                  padding: EdgeInsets.only(bottom: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        child: CustomButton(
                          label: "Skip",
                          borderColor: ColorConstants.silverColor,
                          radius: 50,
                          onTap: () {
                            Get.toNamed(AppRoutes.userLogin);
                          },
                        ),
                      ),
                      Widgets.widthSpaceW3,
                      Widgets.widthSpaceW1,
                      Expanded(
                        child: CustomButton(
                          label: "Next",
                          borderColor: Colors.transparent,
                          backgroundColor: ColorConstants.silverColor,
                          textColor: ColorConstants.blackColor,
                          radius: 50,
                          onTap: () {
                            controller.nextPage(
                                duration: Duration(milliseconds: 500),
                                // curve: Curves.decelerate
                                curve: Curves.linear);
                          },
                        ),
                      ),
                    ],
                  ),
                )
              : Padding(
                  padding: PaddingConstants.screenPaddingHalf,
                  child: CustomButton(
                    label: "Continue",
                    textColor: ColorConstants.blackColor,
                    backgroundColor: ColorConstants.primaryColor,
                    radius: 50,
                    onTap: () {
                      Get.toNamed(AppRoutes.userLogin);
                    },
                  ),
                ),
        )
              ],
            ));
  }
}
