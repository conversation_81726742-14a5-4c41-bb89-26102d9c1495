<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>************-drh2oh0a4gavchbu903e3gs5q8ejam5c.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.************-drh2oh0a4gavchbu903e3gs5q8ejam5c</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>************-e27mmp0jnk3iddj3n8pvlcc38qjt43rd.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyAdInbbOlQDSCGiL0a3rBLkblAVk9H3o4Q</string>
	<key>GCM_SENDER_ID</key>
	<string>************</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.xcodie.indyguide</string>
	<key>PROJECT_ID</key>
	<string>indyguide-5602f</string>
	<key>STORAGE_BUCKET</key>
	<string>indyguide-5602f.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:************:ios:6b00df88880ee7e431176f</string>
</dict>
</plist>